import { injectable, inject } from 'inversify';
import 'reflect-metadata';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { OtpValidationType, UserRole } from '../../common/enums/index.enums';
import TYPES from '@common/types/inversify.types';
import { OtpRepository } from '@v1/repositories/otp.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import CustomError from '@common/helpers/custom-error.helper';
import { CreateUser } from '@common/types/common.types';
import { CreateMerchant, CreateMerchantAccount } from '@common/types/merchant.types';
import UserAccountVerificationMail from '@common/services/email-notifications/common/user-account-verification.service';
import MerchantSignInMail from '@common/services/email-notifications/merchant/sign-in.service';
import { getModifiedMerchant } from '@common/helpers/modify.helper';
import cloudinary from '@common/config/cloudinary.config';
import { convertNameToSentenceCase } from '@common/helpers/names.helper';

@injectable()
class MerchantAccountService {
  private merchantAccountRepository;
  private otpRepository;
  private userRepository;
  private userAccountVerificationMail;
  private merchantSignInMail;

  constructor(
    @inject(TYPES.UserRepository) userRepository: UserRepository,
    @inject(TYPES.OtpRepository) otpRepository: OtpRepository,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.UserAccountVerificationMail) userAccountVerificationMail: UserAccountVerificationMail,
    @inject(TYPES.MerchantSignInMail) merchantSignInMail: MerchantSignInMail,
  ) {
    this.userRepository = userRepository;
    this.otpRepository = otpRepository;
    this.merchantAccountRepository = merchantAccountRepository;
    this.userAccountVerificationMail = userAccountVerificationMail;
    this.merchantSignInMail = merchantSignInMail;
  }

  //separate details going into user and merchant document
  async separateMerchantDetails(merchant: any) {
    const merchantAccountDetails: any = {};
    const userAccountDetails: any = {};
    const commonKeys: string[] = ['phoneNumber', 'lastName', 'firstName', 'email'];

    Object.keys(merchant).forEach((key: string) => {
      if (key === 'merchantId') {
        userAccountDetails['_id'] = merchant.merchantId;
      } else if (key === 'password') {
        userAccountDetails[key] = merchant[key];
      } else if (key === 'businessTypeId') {
        merchantAccountDetails['businessType'] = merchant[key];
      } else if (commonKeys.includes(key)) {
        merchantAccountDetails[key] = merchant[key];
        userAccountDetails[key] = merchant[key];
      } else {
        merchantAccountDetails[key] = merchant[key];
      }
    });

    return { merchantAccountDetails, userAccountDetails };
  }

  async getSecondaryMerchantAccountDetails(merchant: any) {
    const merchantUpdate: any = {};

    Object.keys(merchant).forEach((key: string) => {
      if (key === 'merchantId') {
        merchantUpdate.userId = merchant.merchantId;
      } else {
        merchantUpdate[key] = merchant[key];
      }
    });

    return merchantUpdate;
  }

  async uploadProfilePicture(path: string) {
    try {
      const picture = await cloudinary.uploader.upload(path, {
        folder: 'profile pictures',
      });

      return {
        id: picture.public_id,
        url: picture.secure_url,
      };
    } catch (error) {
      throw new CustomError(500, 'Problem uploading profile picture');
    }
  }

  async createMerchant(user: CreateMerchant, role: UserRole, createdBy: UserRole, file: any = null) {
    try {
      const userFromMail = await this.userRepository.getUserByEmail(user.email);

      if (userFromMail) {
        throw new CustomError(409, 'Email is already tied to an account');
      }

      const newMerchant = await this.separateMerchantDetails({ ...user, firstName: user.firstName.trim(), lastName: user.lastName.trim() });

      const userAccountDetails: CreateUser = newMerchant.userAccountDetails;

      const merchantAccountDetails: CreateMerchantAccount = newMerchant.merchantAccountDetails;

      if (file) {
        userAccountDetails.profilePicture = await this.uploadProfilePicture(file.path);
      }

      const createdUser = await this.userRepository.createUser(userAccountDetails, role);

      merchantAccountDetails.user = createdUser._id.toString();

      const createdMerchant = await this.merchantAccountRepository.create(merchantAccountDetails);

      const userUpdate = { _id: createdUser._id, merchant: createdMerchant._id };

      const updatedUser = await this.userRepository.update(userUpdate);

      const merchant = await getModifiedMerchant(updatedUser, createdMerchant);

      if (createdBy === UserRole.MERCHANT) {
        const code = (await this.otpRepository.createOtp(updatedUser, OtpValidationType.ACCOUNT_VERIFICATION)).otp;

        await this.userAccountVerificationMail.send(user.email, { name: convertNameToSentenceCase(user.firstName), code });
      }

      if (createdBy === UserRole.ADMIN) {
        await this.merchantSignInMail.send(user.email, { name: convertNameToSentenceCase(user.firstName), password: user.password });
      }

      return { user: merchant };
    } catch (error: any) {
      throw new CustomError(error.statusCode || 500, error.message);
    }
  }

  async updateMerchantById(resource: any) {
    const details = await this.separateMerchantDetails(resource);
    const user = await this.userRepository.update(details.userAccountDetails);

    if (!user) {
      throw new CustomError(404, 'Merchant not found');
    }

    details.merchantAccountDetails.userId = user._id;

    return await this.merchantAccountRepository.updateByUserId(details.merchantAccountDetails);
  }

  async setSecondaryAccountDetails(resource: any) {
    const details = await this.getSecondaryMerchantAccountDetails(resource);

    if (!(details.facebookUsername || details.instagramHandle || details.twitterHandle)) {
      throw new CustomError(400, 'Please enter at least one social media handle');
    }
    const user = await this.userRepository.getById(details.userId);

    if (!user) {
      throw new CustomError(404, 'Merchant not found');
    }

    const merchant = await this.merchantAccountRepository.updateByUserId(details);

    return merchant;
  }
}

export { MerchantAccountService };
