import { CommissionSetting } from "@common/schema/index.schema";
import { injectable } from "inversify";
import "reflect-metadata";
import BaseRepository from "./base.repository";
import { Commission } from "@common/types/admin.types";

@injectable()
class CommissionSettingRepository extends BaseRepository {

  constructor(){
    super(CommissionSetting);
  }

  async setCommission(resource: Commission){
    return await CommissionSetting.findOneAndUpdate({},{$set: resource}, {new: true, upsert: true}).exec()
  }

}

export { CommissionSettingRepository };


