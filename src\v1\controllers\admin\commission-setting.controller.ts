import express from 'express';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import { CommissionSettingService } from '@v1/services/admin/commission-setting.service';
import { controller, httpGet, httpPost, httpPut, httpDelete, request, response, httpPatch, requestParam } from 'inversify-express-utils';
import { inject } from 'inversify';
import TYPES from '@common/types/inversify.types';
import Auth from '@common/middleware/auth.middleware';
import { CommissionSchema } from '@v1/dtos/admin/admin-commission.dtos';
import { validate } from '@common/middleware/validate.middleware';
import { logAudit } from '@v1/middleware/admin.middleware';
import { UserRole } from '@common/enums/index.enums';

@controller('/admin/commission')
class CommissionSettingController {
  private commissionSettingService;

  constructor(@inject(TYPES.CommissionSettingService) commissionSettingService: CommissionSettingService) {
    this.commissionSettingService = commissionSettingService;
  }

  @httpPatch('/', Auth.validateAuth([UserRole.SUPER_ADMIN]), validate(CommissionSchema.setCommission), logAudit('Commission setting'))
  public async setCommission(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const commission = await this.commissionSettingService.setCommission(req.body);
      res.handleSuccessResponse(200, 'Commission set', commission);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]))
  public async getCommission(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const commission = await this.commissionSettingService.getCommission();
      res.handleSuccessResponse(200, 'Commission retrieved', commission);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
