import { AdminStatus, MerchantStatus, ServiceDefaultSetting, ServiceStatus, SubscriptionStatus, UserDefaultSetting } from '@common/enums/index.enums';
import mongoose from 'mongoose';

const Schema = mongoose.Schema;

const AdminAccountSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    status: { type: String, default: AdminStatus.NOT_VERIFIED },
  },
  {
    id: false,
    collection: 'admin_accounts',
    timestamps: true,
  },
);

const AuditLogSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    activity: { type: String, required: true, lowercase: true },
  },
  {
    id: false,
    collection: 'audit_logs',
    timestamps: true,
  },
);

const AuthTokenSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    token: { type: String, required: true, unique: true },
    key: Buffer,
    type: { type: String, required: true },
    expiry: { type: Date, required: true },
  },
  {
    id: false,
    timestamps: true,
    collection: 'auth_tokens',
  },
);

const BusinessTypeSchema = new Schema(
  {
    name: { type: String, required: true, lowercase: true },
  },
  {
    id: false,
    collection: 'business_types',
    timestamps: true,
  },
);

const CommissionSettingSchema = new Schema(
  {
    commissionType: { type: String, required: true },
    flatRate: { type: String },
    percentage: { type: String },
  },
  {
    id: false,
    collection: 'commission_settings',
    timestamps: true,
  },
);

const DeletedUserSchema = new Schema(
  {
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    phoneNumber: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    role: { type: String, required: true, default: null },
  },
  {
    id: false,
    timestamps: true,
    collection: 'deleted_users',
  },
);

const InvoiceSchema = new Schema(
  {
    subscriptionPlan: { type: Schema.Types.ObjectId, ref: 'SubscriptionPlan', required: true },
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    merchant: { type: Schema.Types.ObjectId, ref: 'MerchantAccount', required: true },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    status: { type: String, default: SubscriptionStatus.ACTIVE },
    isDeleted: { type: Boolean, default: false, select: false },
  },
  {
    id: false,
    timestamps: true,
  },
);

const MerchantAccountSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    phoneNumber: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    address: { type: String, default: null },
    state: { type: String, default: null },
    businessType: { type: Schema.Types.ObjectId, ref: 'BusinessType', required: true },
    businessName: { type: String, required: true },
    status: { type: String, default: MerchantStatus.NOT_VERIFIED },
    walletId: { type: String, default: null },
    walletClientId: { type: String, default: null },
    walletAccountNumber: { type: String, default: null },
    openingTime: { type: String, default: null  },
    closingTime: { type: String, default: null  },
    openingDays: { type: String, default: null  },
    instagramHandle: { type: String, default: null  },
    twitterHandle: { type: String, default: null  },
    facebookUsername: { type: String, default: null  },
    service: [{ type: Schema.Types.ObjectId, ref: 'Service', select: false }],
    merchantCustomer: [{ type: Schema.Types.ObjectId, ref: 'MerchantCustomer', select: false }],
    order: [{ type: Schema.Types.ObjectId, ref: 'Order', select: false }],
  },
  { id: false, timestamps: true, collection: 'merchant_accounts' },
);

const OrderSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    merchant: { type: Schema.Types.ObjectId, ref: 'MerchantAccount', required: true },
    name: { type: String, required: true },
    service: { type: Schema.Types.ObjectId, ref: 'Service', required: true },
    phoneNumber: { type: String, required: true },
    date: { type: Date, required: true },
    time: { type: String, default: null },
    price: { type: Number, required: true },
    isFullDay: { type: Boolean, required: true },
    isCompleted: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false, select: false },
    deletedAt: { type: Date, select: false },
  },
  { id: false, timestamps: true },
);

const OtpSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    otp: { type: String, required: true, unique: true },
    expiry: { type: Date, required: true },
    validationType: { type: String, required: true },
  },
  { id: false, timestamps: true },
);

const PotentialCustomerSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    merchant: { type: Schema.Types.ObjectId, ref: 'MerchantAccount', required: true },
    name: { type: String, required: true, lowercase: true },
    email: { type: String, lowercase: true },
    phoneNumber: { type: String },
    address: { type: String, lowercase: true },
    serviceRendered: { type: Schema.Types.ObjectId, ref: 'Service' },
    isDeleted: { type: Boolean, default: false, select: false },
    deletedAt: { type: Date, select: false },
  },
  {
    id: false,
    timestamps: true,
    collection: 'potential_customers',
  },
);

const ReminderSchema = new Schema({
  user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  name: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  date: { type: Date, required: true },
  frequency: { type: Number, required: true },
  order: { type: Schema.Types.ObjectId, ref: 'Order', required: true },
});

const ServiceSchema = new Schema(
  {
    user: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    merchant: { type: Schema.Types.ObjectId, ref: 'MerchantAccount', required: true },
    name: { type: String, required: true },
    description: { type: String, required: true },
    duration: { type: String, required: true },
    price: { type: Number, required: true },
    reminderFrequency: { type: Number, required: true },
    reviewImage: {
      id: { type: String, default: null },
      url: { type: String, default: ServiceDefaultSetting.reviewImageUrl },
    },
    status: { type: String, default: ServiceStatus.ACTIVE },
    isNegotiable: { type: Boolean, required: true },
    isDeleted: { type: Boolean, default: false, select: false },
    deletedAt: { type: Date, select: false },
  },
  { id: false, timestamps: true },
);

const SubscriptionPlanSchema = new Schema(
  {
    name: { type: String, required: true },
    amountPerMonth: { type: Number, required: true },
    description: { type: String, required: true },
    features: { type: String, default: null },
  },
  {
    id: false,
    timestamps: true,
    collection: 'subscription_plans',
  },
);

const UserSchema = new Schema(
  {
    firstName: { type: String, default: null },
    lastName: { type: String, default: null },
    phoneNumber: { type: String, default: null },
    email: { type: String, required: true, unique: true },
    role: { type: String, required: true, default: null },
    password: { type: String, select: false, default: null },
    isVerified: { type: Boolean, default: false },
    lastLoggedInAt: { type: Date, default: null },
    profilePicture: {
      id: { type: String, default: null },
      url: { type: String, default: UserDefaultSetting.profilePictureUrl },
    },
    createdAt: Date,
    updatedAt: Date,
    verifiedAt: Date,
    otp: [{ type: Schema.Types.ObjectId, ref: 'Otp', select: false }],
    authToken: [{ type: Schema.Types.ObjectId, ref: 'AuthToken', select: false }],
    merchant: { type: Schema.Types.ObjectId, ref: 'MerchantAccount' },
    admin: { type: Schema.Types.ObjectId, ref: 'AdminAccount' },
  },
  { id: false, timestamps: true },
);

export const AdminAccount = mongoose.model('AdminAccount', AdminAccountSchema);
export const AuditLog = mongoose.model('AuditLog', AuditLogSchema);
export const AuthToken = mongoose.model('AuthToken', AuthTokenSchema);
export const BusinessType = mongoose.model('BusinessType', BusinessTypeSchema);
export const CommissionSetting = mongoose.model('CommissionType', CommissionSettingSchema);
export const DeletedUser = mongoose.model('DeletedUser', DeletedUserSchema);
export const Invoice = mongoose.model('Invoice', InvoiceSchema);
export const MerchantAccount = mongoose.model('MerchantAccount', MerchantAccountSchema);
export const PotentialCustomer = mongoose.model('PotentialCustomer', PotentialCustomerSchema);
export const SubscriptionPlan = mongoose.model('SubscriptionPlan', SubscriptionPlanSchema);
export const Order = mongoose.model('Order', OrderSchema);
export const Otp = mongoose.model('Otp', OtpSchema);
export const Reminder = mongoose.model('Reminder', ReminderSchema);
export const Service = mongoose.model('Service', ServiceSchema);
export const User = mongoose.model('User', UserSchema);
