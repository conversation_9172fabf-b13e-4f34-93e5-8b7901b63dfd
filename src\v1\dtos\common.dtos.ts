import { z as schema } from 'zod';

export const emailBody = schema.object({
  body: schema
    .object({
      email: schema.string().email(),
    })
    .strict(),
});

export const _idParam = schema.object({
  params: schema
    .object({
      _id: schema.string(),
    })
    .strict(),
});

export const _idBody = schema.object({
  body: schema
    .object({
      _id: schema.string(),
    })
    .strict(),
});

export const paginationFilter = schema.object({
  query: schema
    .object({
      page: schema.string(),
    })
    .strict(),
});

export const merchantIdFilter = schema.object({
  query: schema
    .object({
      page: schema.string(),
    })
    .strict(),
  params: schema
    .object({
      merchant_id: schema.string(),
    })
    .strict(),
});

export const genericFilter = schema.object({
  query: schema
    .object({
      page: schema.string().optional(),
      limit: schema.string().optional(),
      startDate: schema.string().optional(),
      endDate: schema.string().optional(),
    })
    .strict(),
});

export const filterUsers = schema.object({
  query: schema
    .object({
      page: schema.string().optional(),
      limit: schema.string().optional(),
      startDate: schema.string().optional(),
      endDate: schema.string().optional(),
      status: schema.string().optional(),
    })
    .strict(),
});

export const dashboardSearch = schema.object({
  query: schema
    .object({
      page: schema.string().optional(),
      limit: schema.string().optional(),
      word: schema.string(),
    })
    .strict(),
});

export const appSearch = schema.object({
  query: schema
    .object({
      page: schema.string(),
      word: schema.string(),
    })
    .strict(),
});
