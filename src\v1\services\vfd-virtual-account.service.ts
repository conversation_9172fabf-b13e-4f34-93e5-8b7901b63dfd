import env from '@common/config/env.config';
import { RequestMethods } from '@common/enums/index.enums';
import { makeApiCall } from '@common/helpers/call-api.helper';
import CustomError from '@common/helpers/custom-error.helper';
import { addADayToDate, isValidYYYMMDDDate } from '@common/helpers/dates.helper';
import { carbonTransferPayload, createVfdAccount, requestMethod } from '@common/types/common.types';
import { VirtualAccount } from '@v1/interfaces/virtual-account.interface';
import cryptojs from 'crypto-js';
import { injectable } from 'inversify';

@injectable()
export default class VFDVirtualAccount implements VirtualAccount {
  constructor(private baseUrl: string = env.VFD_BASE_URL || '') {}

  async fetchListOfBanks() {
    const urlPath = env.CARBON_FETCH_BANKS;

    const headers = await this.generateRequestHeaders(RequestMethods.GET, urlPath);

    return await makeApiCall(RequestMethods.GET, this.baseUrl + urlPath, headers);
  }

  async createAccount(accountOwner: createVfdAccount) {
    const urlPath = env.CARBON_CREATE_WALLET_PATH ? env.CARBON_CREATE_WALLET_PATH : '';

    let payload = {
      bvn: accountOwner.bvn,
      dateOfBirth: accountOwner.dateOfBirthOnBVn
    };

    const headers = await this.generateRequestHeaders(RequestMethods.POST, urlPath, payload);

    const virtualAccount = await makeApiCall(RequestMethods.POST, this.baseUrl + urlPath, headers, payload);

    return virtualAccount;
  }

  async fetchWalletDetails(walletId: string) {
    const urlPath = env.CARBON_FETCH_WALLET_DETAILS_PATH + walletId;

    const headers = await this.generateRequestHeaders(RequestMethods.GET, urlPath);

    return await makeApiCall(RequestMethods.GET, this.baseUrl + urlPath, headers);
  }

  async fetchWalletStatement(walletId: string, startDate: string, endDate: string, limit: string) {
    if (!isValidYYYMMDDDate(startDate) || !isValidYYYMMDDDate(endDate)) {
      throw new CustomError(400, 'Enter valid start and end dates in the format YYYY-MM-DD');
    }

    startDate = addADayToDate(startDate); //carbon subtracts a day from inputted dates so we add a day to get the accurrate results from their endpoint
    endDate = addADayToDate(endDate);

    const urlPath = env.CARBON_FETCH_WALLET_STATEMENT_PATH.replace('{walletId}', walletId);

    const urlQueryString = this.baseUrl + urlPath + `?walletId=${walletId}&startDate=${startDate}&endDate=${endDate}&offset=0&limit=${limit}`;

    const headers = await this.generateRequestHeaders(RequestMethods.GET, urlPath);

    return await makeApiCall(RequestMethods.GET, urlQueryString, headers);
  }

  async transferFunds(transfer: carbonTransferPayload) {
    const urlPath = env.CARBON_TRANSFER_PATH;

    let payload = {
      transaction_amount: transfer.amount * 100, //convert from naira to kobo
      source_account_id: transfer.walletId,
      beneficiary_account_number: transfer.beneficiaryAccountNumber,
      beneficiary_bank_code: transfer.beneficiaryBankSortCode,
      transaction_narration: transfer.narration,
    };

    const headers = await this.generateRequestHeaders(RequestMethods.POST, urlPath, payload);

    const response = await makeApiCall(RequestMethods.POST, this.baseUrl + urlPath, headers, payload);

    return response;
  }

  async activateWallet(walletId: string, reasonForActivation: string) {
    const urlPath = env.CARBON_ACTIVATE_WALLET_PATH + reasonForActivation;

    const payload = { walletId };

    const headers = await this.generateRequestHeaders(RequestMethods.POST, urlPath, payload);

    return await makeApiCall(RequestMethods.POST, this.baseUrl + urlPath, headers, payload);
  }

  async deactivateWallet(walletId: string, reasonForDeactivation: string) {
    const urlPath = env.CARBON_DEACTIVATE_WALLET_PATH + reasonForDeactivation;

    const payload = { walletId };

    const headers = await this.generateRequestHeaders(RequestMethods.POST, urlPath, payload);

    return await makeApiCall(RequestMethods.POST, this.baseUrl + urlPath, headers, payload);
  }

  private async generateRequestHeaders(requestMethod: requestMethod, path: string, requestObject?: any) {
    let requestRawBody = requestMethod == RequestMethods.GET ? '' : JSON.stringify(requestObject);

    let dateHeaderValue = new Date().toUTCString();

    let bodyDigest = cryptojs.SHA256(requestRawBody).toString(cryptojs.enc.Base64);

    let digestHeaderValue = `SHA-256=${bodyDigest}`;

    let requestLine = requestMethod === RequestMethods.GET ? `GET ${path} HTTP/1.1` : `POST ${path} HTTP/1.1`;

    let hostUrl = this.baseUrl ? this.baseUrl : '';
    let apiKey = '';

    let signingString = [`date: ${dateHeaderValue}`, `digest: ${digestHeaderValue}`, `host: ${new URL(hostUrl).hostname}`, requestLine].join('\n');

    let signature = cryptojs.HmacSHA256(signingString, apiKey).toString(cryptojs.enc.Base64);

    let hMACAuth = `hmac`;

    const headers = {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Date: dateHeaderValue,
      Digest: digestHeaderValue,
      Authorization: hMACAuth,
    };

    return headers;
  }
}
