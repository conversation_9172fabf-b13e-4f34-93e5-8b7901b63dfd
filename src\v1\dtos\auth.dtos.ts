import { z as schema } from 'zod';

const email = schema.object({
  body: schema
    .object({
      email: schema.string().email(),
    })
    .strict(),
});

const verifyAccount = schema.object({
  body: schema
    .object({
      email: schema.string().email(),
      code: schema.string(),
    })
    .strict(),
});

const resetPassword = schema.object({
  body: schema
    .object({
      code: schema.string(),
      email: schema.string(),
      password: schema.string(),
    })
    .strict(),
});

const changePassword = schema.object({
  body: schema
    .object({
      code: schema.string(),
      currentPassword: schema.string(),
      newPassword: schema.string(),
    })
    .strict(),
});

const changeEmail = schema.object({
  body: schema
    .object({
      code: schema.string(),
      newEmail: schema.string(),
    })
    .strict(),
});

const signIn = schema.object({
  body: schema
    .object({
      email: schema.string().email(),
      password: schema.string(),
    })
    .strict(),
});

const merchantSignUp = schema.object({
  body: schema
    .object({
      firstName: schema.string(),
      lastName: schema.string(),
      password: schema.string(),
      businessTypeId: schema.string(),
      businessName: schema.string(),
      email: schema.string().email(), // Ensuring the email is a valid email format
      phoneNumber: schema.string(), // Optionally, you can add more specific validation here if needed
      address: schema.string(),
      state: schema.string(),
    })
    .strict(),
});

const refreshToken = schema.object({
  body: schema
    .object({
      refreshToken: schema.string(),
    })
    .strict(),
});

export const AuthSchema = {
  verifyAccount,
  changePassword,
  resetPassword,
  refreshToken,
  email,
  signIn,
  merchantSignUp,
  changeEmail,
};
