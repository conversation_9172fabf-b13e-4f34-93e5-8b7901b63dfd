import { AdminAccount } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class AdminAccountRepository extends BaseRepository {
  constructor() {
    super(AdminAccount);
  }

  async findAdmins(filter: any, limit: number = 1, page: number = 1) {
    return await AdminAccount.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate('user', '-createdAt -updatedAt -admin -merchant -__v')
      .select('-_id -__v')
      .sort('-createdAt')
      .exec();
  }

  async updateByUserId(resource: any) {
    const { userId, ...data } = resource;
    return await AdminAccount.findOneAndUpdate({ user: userId }, { $set: data }, { new: true })
      .populate('user', '-createdAt -updatedAt -admin -merchant -__v')
      .select('-_id -__v')
      .exec();
  }

  async getByUserId(userId: string) {
    return await AdminAccount.findOne({ user: userId }).populate('user', '-createdAt -updatedAt -admin -merchant -__v').select('-_id -__v').exec();
  }

  async deleteByUserId(userId: string) {
    return await AdminAccount.findOneAndDelete({ user: userId }).exec();
  }

  async getAdmins(page: number = 1, limit: number = 10, startDate: Date = new Date(1970), endDate: Date = new Date(), status: string | null = null) {
    let data, filter;

    if (status) {
      filter = { createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }, status: status.toUpperCase() };

      data = await this.findAdmins(filter, limit, page);

      return await this.getPaginationDetails(filter, limit, page, data);
    }

    filter = { createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) } };

    data = await this.findAdmins(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }
}

export { AdminAccountRepository };

export default new AdminAccountRepository();
