import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class MerchantWelcomeMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string }): Promise<any> {
    this.emailNotifier.setMailProperties('Welcome to Wadoo!', 'wadoo.merchant.welcome', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default MerchantWelcomeMail;
