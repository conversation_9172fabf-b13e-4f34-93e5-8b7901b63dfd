import { injectable, inject } from 'inversify';
import 'reflect-metadata';
import { ReminderRepository } from '@v1/repositories/reminder.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import TYPES from '@common/types/inversify.types';
import CustomError from '@common/helpers/custom-error.helper';
import cron from 'node-cron';

@injectable()
class ReminderService {
  private reminderRepository;
  private userRepository;

  constructor(
    @inject(TYPES.ReminderRepository) reminderRepository: ReminderRepository,
    @inject(TYPES.UserRepository) userRepository: UserRepository,
  ) {
    this.reminderRepository = reminderRepository;
    this.userRepository = userRepository;
  }

  async getReminders(merchantId: string) {
    const merchant = await this.userRepository.getById(merchantId);

    if (!merchant) {
      throw new CustomError(404, 'Merchant not found');
    }

    return await this.reminderRepository.getRemindersByMerchantId(merchantId);
  }

  async updateReminders() {
    cron.schedule(
      `0 3 * * *`,
      async () => {
        const reminders = await this.reminderRepository.getDueReminders();
        reminders.forEach(async (reminder: any) => {
          await this.reminderRepository.updateReminder(reminder);
        });
      },
      {
        scheduled: true,
        timezone: 'Africa/Lagos',
      },
    );
  }
}

export { ReminderService };
