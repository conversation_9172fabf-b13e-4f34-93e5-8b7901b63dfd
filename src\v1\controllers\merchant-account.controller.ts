import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import UploadMiddleware from '@common/middleware/upload.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { MerchantSchema } from '@v1/dtos/merchant.dtos';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { UserService } from '@v1/services/user.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpDelete, httpGet, httpPatch, httpPost, request, response } from 'inversify-express-utils';
import { MerchantAccountService } from '../services/merchant-account.service';

@controller('/merchant', Auth.validateAuth([UserRole.MERCHANT]))
export class MerchantController {
  private userService;
  private merchantAccountService;
  private merchantAccountRepository;

  constructor(
    @inject(TYPES.UserService) userService: UserService,
    @inject(TYPES.MerchantAccountService) merchantAccountService: MerchantAccountService,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
  ) {
    this.userService = userService;
    this.merchantAccountService = merchantAccountService;
    this.merchantAccountRepository = merchantAccountRepository;
  }

  @httpPost('/', validate(MerchantSchema.createMerchant))
  public async createMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.createMerchant(req.body, UserRole.MERCHANT, UserRole.MERCHANT);
      res.handleSuccessResponse(201, 'Registration successful', merchant);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/:merchant_id', validate(MerchantSchema.merchantId))
  public async getMerchantById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountRepository.getByUserId(req.params.merchant_id);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Merchant retrieved`, merchant);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/', validate(MerchantSchema.updateMerchant))
  public async updateMerchantById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.updateMerchantById(req.body);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Merchant updated`, merchant);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/social-media-handles', validate(MerchantSchema.socialMediaHandles))
  public async setSocialMediaHandles(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.setSecondaryAccountDetails(req.body);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Social media handles of merchant set`, merchant);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/profile-picture', UploadMiddleware.uploadProfilePicture(), validate(MerchantSchema.bodyId))
  public async updateProfilePicture(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.userService.updateProfilePicture(req.body.merchantId, req.file);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Profile picture updated`, merchant);
    } catch (error: any) {}
  }

  @httpPatch('/opening-hours', validate(MerchantSchema.setOpeningTimesAndDays))
  public async setOpeningHours(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.setSecondaryAccountDetails(req.body);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Opening hours of merchant set`, merchant);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete('/:merchant_id', validate(MerchantSchema.merchantId))
  public async deleteMerchantById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.userService.deleteUserById(req.params.merchant_id);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Merchant deleted`);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
}
