import cloudinary from '@common/config/cloudinary.config';
import CustomError from '@common/helpers/custom-error.helper';
import { getModifiedUser } from '@common/helpers/modify.helper';
import { convertNameToSentenceCase } from '@common/helpers/names.helper';
import UserAccountVerificationMail from '@common/services/email-notifications/common/user-account-verification.service';
import TYPES from '@common/types/inversify.types';
import { AdminAccountRepository } from '@v1/repositories/admin-account.repository';
import { DeletedUserRepository } from '@v1/repositories/deleted-user.repository';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { OrderRepository } from '@v1/repositories/order.repository';
import { OtpRepository } from '@v1/repositories/otp.repository';
import { PotentialCustomerRepository } from '@v1/repositories/potential-customer.repository';
import { ReminderRepository } from '@v1/repositories/reminder.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import argon2 from 'argon2';
import { inject, injectable } from 'inversify';
import 'reflect-metadata';
import { AdminStatus, OtpValidationType, UserDefaultSetting, UserRole } from '@common/enums/index.enums';
import mongoose from 'mongoose';
import { Image } from '@common/types/common.types';

@injectable()
class UserService {
  private userRepository;
  private deletedUserRepository;
  private otpRepository;
  private merchantAccountRepository;
  private userAccountVerificationMail;
  private adminAccountRepository;
  private orderRepository;
  private serviceRepository;
  private potentialCustomerRepository;
  private reminderRepository;

  constructor(
    @inject(TYPES.UserRepository) userRepository: UserRepository,
    @inject(TYPES.OtpRepository) otpRepository: OtpRepository,
    @inject(TYPES.DeletedUserRepository) deletedUserRepository: DeletedUserRepository,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.UserAccountVerificationMail) userAccountVerificationMail: UserAccountVerificationMail,
    @inject(TYPES.AdminAccountRepository) adminAccountRepository: AdminAccountRepository,
    @inject(TYPES.OrderRepository) orderRepository: OrderRepository,
    @inject(TYPES.ServiceRepository) serviceRepository: ServiceRepository,
    @inject(TYPES.PotentialCustomerRepository) potentialCustomerRepository: PotentialCustomerRepository,
    @inject(TYPES.ReminderRepository) reminderRepository: ReminderRepository,
  ) {
    this.userRepository = userRepository;
    this.deletedUserRepository = deletedUserRepository;
    this.otpRepository = otpRepository;
    this.merchantAccountRepository = merchantAccountRepository;
    this.userAccountVerificationMail = userAccountVerificationMail;
    this.adminAccountRepository = adminAccountRepository;
    this.orderRepository = orderRepository;
    this.serviceRepository = serviceRepository;
    this.potentialCustomerRepository = potentialCustomerRepository;
    this.reminderRepository = reminderRepository;
  }

  private async sendOtp(user: any) {
    const otp = await this.otpRepository.createOtp(user, OtpValidationType.ACCOUNT_VERIFICATION);
    this.userAccountVerificationMail.send(user.email, { name: convertNameToSentenceCase(user.firstName), code: otp.otp });
  }

  async createUser(user: any, role: string | null = null) {
    const newUser = await this.userRepository.createUser(user, role);
    const modifiedUser = await getModifiedUser(newUser);
    await this.sendOtp(modifiedUser);
    return { user: modifiedUser };
  }

  async updateProfilePicture(id: string, file: any) {
    let profilePicture: Image;

    const user = await this.userRepository.getById(id);

    if (!user) {
      return null;
    }

    if (user.profilePicture.id && user.profilePicture.url) {
      await cloudinary.uploader.destroy(user.profilePicture.id);
    }

    if (file) {
      const cloudinaryPicture = await cloudinary.uploader.upload(file.path, {
        folder: 'profile pictures',
      });
      profilePicture = {
        id: cloudinaryPicture.public_id,
        url: cloudinaryPicture.secure_url,
      };
    } else {
      profilePicture = {
        id: null,
        url: UserDefaultSetting.profilePictureUrl,
      };
    }

    const updatedUser = await this.userRepository.update({ _id: id, profilePicture });

    if (user.role === UserRole.ADMIN) {
      return await this.adminAccountRepository.getByUserId(user._id);
    }

    if (user.role === UserRole.MERCHANT) {
      return await this.merchantAccountRepository.getByUserId(user._id);
    }

    if (user.role === UserRole.SUPER_ADMIN) {
      return await getModifiedUser(updatedUser);
    }
  }

  async updateUserById(resource: any, isAdminRegistration: boolean = false) {
    const { confirmPassword, ...updateData } = resource;

    if (!mongoose.Types.ObjectId.isValid(updateData._id)) {
      throw new CustomError(404, 'User not found');
    }

    let user = await this.userRepository.getById(updateData._id);

    if (!user) {
      throw new CustomError(404, 'User not found');
    }

    if (isAdminRegistration) {
      if (updateData.password !== confirmPassword) {
        throw new CustomError(400, 'password and confirmPassword do not match');
      }

      if (user.firstName || user.lastName) {
        throw new CustomError(409, 'Admin already registered');
      }

      updateData.isVerified = true;
      await this.adminAccountRepository.updateByUserId({ userId: updateData._id, status: AdminStatus.ACTIVE });
    }

    if (updateData.password) {
      // hash if necessary
      updateData.isVerified = true;
      updateData.password = await argon2.hash(updateData.password);
    }

    if (updateData.firstName) {
      updateData.firstName = updateData.firstName.trim();
    }

    if (updateData.lastName) {
      updateData.lastName = updateData.lastName.trim();
    }

    const updatedUser = await this.userRepository.update(updateData);

    // TODO
    // if (
    //   (isAdminRegistration && [UserRole.ADMIN, UserRole.ANALYST].includes(user.role)) ||
    //   ([UserRole.ADMIN, UserRole.ANALYST].includes(user.role) && !user.isVerified)
    // ) {
    //   await this.sendOtp(updatedUser);
    // }    // Removed pending further confirmation

    if ([UserRole.ADMIN, UserRole.ANALYST].includes(user.role) && !isAdminRegistration) {
      const admin = await this.adminAccountRepository.getByUserId(user._id);
      return admin;
    }

    return await getModifiedUser(updatedUser);
  }

  async deleteUserById(id: string) {
    const user: any = await this.userRepository.getById(id);

    if (user) {
      const deletedUser = {
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phoneNumber: user.phoneNumber,
        role: user.role,
      };

      await this.deletedUserRepository.create(deletedUser);
      await this.userRepository.delete(id);

      if (user.role === UserRole.MERCHANT) {
        await this.merchantAccountRepository.deleteByUserId(id);
        await this.potentialCustomerRepository.deleteMany(id);
        await this.serviceRepository.deleteMany(id);
        await this.orderRepository.deleteMany(id);
        await this.reminderRepository.deleteMany(id);
      }

      if (user.role === UserRole.ADMIN) {
        await this.adminAccountRepository.deleteByUserId(id);
      }
    }

    return user;
  }

  async getUserById(resource: any) {
    let user = await this.userRepository.getById(resource._id);

    return user;
  }

  async getUsers(resource: any) {
    return await this.userRepository.list(resource.page, resource.limit, resource?.startDate, resource?.endDate);
  }
}

export { UserService };
