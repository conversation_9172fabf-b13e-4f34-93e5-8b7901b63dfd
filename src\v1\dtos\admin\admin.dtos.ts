import { z as schema } from 'zod';

const createProspectAdminUser = schema.object({
  body: schema
    .object({
      email: schema.string().email(),
      role: schema.enum(['ADMIN', 'ANALYST']),
    })
    .strict(),
});

const registerAdmin = schema.object({
  body: schema
    .object({
      _id: schema.string(),
      firstName: schema.string(),
      lastName: schema.string(),
      phoneNumber: schema.string(),
      password: schema.string(),
      confirmPassword: schema.string(),
    })
    .strict(),
});

const updateAdmin = schema.object({
  body: schema
    .object({
      firstName: schema.string().optional(),
      lastName: schema.string().optional(),
      phoneNumber: schema.string().optional(),
      email: schema.string().email().optional(),
    })
    .strict(),
});

const updateAdminPassword = schema.object({
  body: schema
    .object({
      currentPassword: schema.string(),
      newPassword: schema.string(),
      confirmNewPassword: schema.string(),
    })
    .strict(),
});

const updateAdminBySuperAdmin = schema.object({
  body: schema
    .object({
      _id: schema.string(),
      firstName: schema.string().optional(),
      lastName: schema.string().optional(),
      phoneNumber: schema.string().optional(),
      email: schema.string().email().optional(),
    })
    .strict(),
});

const _id = schema.object({
  params: schema
    .object({
      _id: schema.string(),
    })
    .strict(),
});

export const AdminSchema = {
  updateAdminBySuperAdmin,
  createProspectAdminUser,
  registerAdmin,
  updateAdmin,
  updateAdminPassword,
  _id,
};
