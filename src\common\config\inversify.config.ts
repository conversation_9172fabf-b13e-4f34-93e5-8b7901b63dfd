import { DatabaseConnection } from '@common/interfaces/database-connection.interface';
import { DatabaseSeeder } from '@common/interfaces/database-seeder.interface';
import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import { AuthMiddleware } from '@common/middleware/auth.middleware';
import SendAdminInviteMail from '@common/services/email-notifications/admin/invite.service';
import AdminWelcomeMail from '@common/services/email-notifications/admin/welcome.service';
import UserAccountVerificationMail from '@common/services/email-notifications/common/user-account-verification.service';
import EmailChangeSuccessMail from '@common/services/email-notifications/common/user-email-change-success.service';
import EmailChangeRequestMail from '@common/services/email-notifications/common/user-email-change.service';
import UserPasswordChangeMail from '@common/services/email-notifications/common/user-password-change.service';
import UserPasswordResetMail from '@common/services/email-notifications/common/user-password-reset.service';
import UserSuccessfulPasswordChangeMail from '@common/services/email-notifications/common/user-successful-password-change.service';
import MerchantSignInMail from '@common/services/email-notifications/merchant/sign-in.service';
import MerchantWelcomeMail from '@common/services/email-notifications/merchant/welcome.service';
import MailgunNotifier from '@common/services/mailgun-notifier.service';
import MongooseConnect from '@common/services/mongoose-connect.service';
import MongooseSeeder from '@common/services/mongoose-seeder.service';
import { PictureUploadService } from '@common/services/picture-upload.service';
import { SeedDatabase } from '@common/services/seed-database.service';
import TYPES from '@common/types/inversify.types';
import { VirtualAccount } from '@v1/interfaces/virtual-account.interface';
import { AdminAccountRepository } from '@v1/repositories/admin-account.repository';
import { AuditLogRepository } from '@v1/repositories/audit-log.repository';
import { AuthTokenRepository } from '@v1/repositories/auth-token.repository';
import { BusinessTypeRepository } from '@v1/repositories/business-type.repository';
import { CommissionSettingRepository } from '@v1/repositories/commission-setting.repository';
import { DeletedUserRepository } from '@v1/repositories/deleted-user.repository';
import { InvoiceRepository } from '@v1/repositories/invoice.repository';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { OrderRepository } from '@v1/repositories/order.repository';
import { OtpRepository } from '@v1/repositories/otp.repository';
import { PotentialCustomerRepository } from '@v1/repositories/potential-customer.repository';
import { ReminderRepository } from '@v1/repositories/reminder.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { SubscriptionPlanRepository } from '@v1/repositories/subscription-plan.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import { AdminService } from '@v1/services/admin/admin.service';
import { AuditLogService } from '@v1/services/admin/audit-log.service';
import { AdminBusinessTypeService } from '@v1/services/admin/business-type.service';
import { CommissionSettingService } from '@v1/services/admin/commission-setting.service';
import { DashboardService } from '@v1/services/admin/dashboard.service';
import { AdminInvoiceService } from '@v1/services/admin/invoice.service';
import { AdminDashboardService } from '@v1/services/admin/merchant-service.service';
import { AdminMerchantService } from '@v1/services/admin/merchant.service';
import { AdminPotentialCustomerService } from '@v1/services/admin/potential-customer.service';
import { AdminSubscriptionPlanService } from '@v1/services/admin/subscription-plan.service';
import { AuthService } from '@v1/services/auth.service';
import { BusinessTypeService } from '@v1/services/business-type.service';
import CarbonVirtualAccount from '@v1/services/carbon-virtual-account.service';
import { InvoiceService } from '@v1/services/invoice.service';
import { MerchantAccountService } from '@v1/services/merchant-account.service';
import { MerchantService } from '@v1/services/merchant-service.service';
import { OrderService } from '@v1/services/order.service';
import { PotentialCustomerService } from '@v1/services/potential-customer.service';
import { ReminderService } from '@v1/services/reminder.service';
import { SubscriptionPlanService } from '@v1/services/subscription-plan.service';
import { UserService } from '@v1/services/user.service';
import { WalletService } from '@v1/services/wallet.service';
import { Container } from 'inversify';
import 'reflect-metadata';

// bindings
const container = new Container();
// vendors
container.bind<DatabaseConnection>(TYPES.DatabaseConnectionType).to(MongooseConnect);
container.bind<DatabaseSeeder>(TYPES.DatabaseSeeder).to(MongooseSeeder);
container.bind<SeedDatabase>(TYPES.SeedDatabase).toConstantValue(new SeedDatabase());
container.bind<EmailNotifier>(TYPES.EmailNotifier).to(MailgunNotifier);
container.bind<VirtualAccount>(TYPES.VirtualAccount).to(CarbonVirtualAccount);

// repositories
container.bind<AdminAccountRepository>(TYPES.AdminAccountRepository).to(AdminAccountRepository);
container.bind<AuditLogRepository>(TYPES.AuditLogRepository).to(AuditLogRepository);
container.bind<AuthTokenRepository>(TYPES.AuthTokenRepository).to(AuthTokenRepository);
container.bind<BusinessTypeRepository>(TYPES.BusinessTypeRepository).to(BusinessTypeRepository);
container.bind<CommissionSettingRepository>(TYPES.CommissionSettingRepository).to(CommissionSettingRepository);
container.bind<DeletedUserRepository>(TYPES.DeletedUserRepository).to(DeletedUserRepository);
container.bind<InvoiceRepository>(TYPES.InvoiceRepository).to(InvoiceRepository);
container.bind<MerchantAccountRepository>(TYPES.MerchantAccountRepository).to(MerchantAccountRepository);
container.bind<OrderRepository>(TYPES.OrderRepository).to(OrderRepository);
container.bind<OtpRepository>(TYPES.OtpRepository).to(OtpRepository);
container.bind<PotentialCustomerRepository>(TYPES.PotentialCustomerRepository).to(PotentialCustomerRepository);
container.bind<ReminderRepository>(TYPES.ReminderRepository).to(ReminderRepository);
container.bind<SubscriptionPlanRepository>(TYPES.SubscriptionPlanRepository).to(SubscriptionPlanRepository);
container.bind<ServiceRepository>(TYPES.ServiceRepository).to(ServiceRepository);
container.bind<UserRepository>(TYPES.UserRepository).to(UserRepository);

//services
container.bind<AdminBusinessTypeService>(TYPES.AdminBusinessTypeService).to(AdminBusinessTypeService);
container.bind<AdminDashboardService>(TYPES.AdminDashboardService).to(AdminDashboardService);
container.bind<AdminMerchantService>(TYPES.AdminMerchantService).to(AdminMerchantService);
container.bind<AdminInvoiceService>(TYPES.AdminInvoiceService).to(AdminInvoiceService);
container.bind<AdminPotentialCustomerService>(TYPES.AdminPotentialCustomerService).to(AdminPotentialCustomerService);
container.bind<AdminService>(TYPES.AdminService).to(AdminService);
container.bind<AdminSubscriptionPlanService>(TYPES.AdminSubscriptionPlanService).to(AdminSubscriptionPlanService);
container.bind<AuditLogService>(TYPES.AuditLogService).to(AuditLogService);
container.bind<AuthService>(TYPES.AuthService).to(AuthService);
container.bind<BusinessTypeService>(TYPES.BusinessTypeService).to(BusinessTypeService);
container.bind<CommissionSettingService>(TYPES.CommissionSettingService).to(CommissionSettingService);
container.bind<DashboardService>(TYPES.DashboardService).to(DashboardService);
container.bind<InvoiceService>(TYPES.InvoiceService).to(InvoiceService);
container.bind<MerchantAccountService>(TYPES.MerchantAccountService).to(MerchantAccountService);
container.bind<MerchantService>(TYPES.MerchantService).to(MerchantService);
container.bind<OrderService>(TYPES.OrderService).to(OrderService);
container.bind<PictureUploadService>(TYPES.PictureUploadService).to(PictureUploadService);
container.bind<PotentialCustomerService>(TYPES.PotentialCustomerService).to(PotentialCustomerService);
container.bind<ReminderService>(TYPES.ReminderService).to(ReminderService);
container.bind<SubscriptionPlanService>(TYPES.SubscriptionPlanService).to(SubscriptionPlanService);
container.bind<UserService>(TYPES.UserService).to(UserService);
container.bind<WalletService>(TYPES.WalletService).to(WalletService);

container.bind<AuthMiddleware>(TYPES.AuthMiddleware).to(AuthMiddleware);

//mails
container.bind<AdminWelcomeMail>(TYPES.AdminWelcomeMail).to(AdminWelcomeMail);
container.bind<EmailChangeRequestMail>(TYPES.EmailChangeRequestMail).to(EmailChangeRequestMail);
container.bind<EmailChangeSuccessMail>(TYPES.EmailChangeSuccessMail).to(EmailChangeSuccessMail);
container.bind<MerchantSignInMail>(TYPES.MerchantSignInMail).to(MerchantSignInMail);
container.bind<MerchantWelcomeMail>(TYPES.MerchantWelcomeMail).to(MerchantWelcomeMail);
container.bind<SendAdminInviteMail>(TYPES.SendAdminInviteMail).to(SendAdminInviteMail);
container.bind<UserAccountVerificationMail>(TYPES.UserAccountVerificationMail).to(UserAccountVerificationMail);
container.bind<UserPasswordChangeMail>(TYPES.UserPasswordChangeMail).to(UserPasswordChangeMail);
container.bind<UserPasswordResetMail>(TYPES.UserPasswordResetMail).to(UserPasswordResetMail);
container.bind<UserSuccessfulPasswordChangeMail>(TYPES.UserSuccessfulPasswordChangeMail).to(UserSuccessfulPasswordChangeMail);

export default container;

export const databaseCleanUp = container.get<AuthService>(TYPES.AuthService);
export const databaseConnection = container.get<DatabaseConnection>(TYPES.DatabaseConnectionType);
export const databaseSeeder = container.get<DatabaseSeeder>(TYPES.DatabaseSeeder);
export const merchantReminder = container.get<ReminderService>(TYPES.ReminderService);
export const merchantSubscriptionsCleanup = container.get<InvoiceService>(TYPES.InvoiceService)
export const pictureUploadCleanUp = container.get<PictureUploadService>(TYPES.PictureUploadService);
export const seedDatabase = container.get<SeedDatabase>(TYPES.SeedDatabase);
