import { convertInputToLowercase } from '@common/helpers/convert-input-to-lowercase.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import express from 'express';
import { AnyZodObject } from 'zod';

export const validate = (schema: AnyZodObject) => {
  return async (req: express.Request, res: CustomResponse, next: express.NextFunction) => {
    try {
      await schema.parseAsync({
        body: req.body,
        query: req.query,
        params: req.params,
      });

      if (Object.keys(req.body).length !== 0) {
        req.body = convertInputToLowercase(req.body);
      }

      if (Object.keys(req.query).length !== 0) {
        req.query = convertInputToLowercase(req.query);
      }

      if (Object.keys(req.params).length !== 0) {
        req.params = convertInputToLowercase(req.params);
      }

      next();
    } catch (error) {
      res.handleErrorResponse(error);
    }
  };
};
