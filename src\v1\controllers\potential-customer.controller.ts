import express from 'express';
import { PotentialCustomerService } from '@v1/services/potential-customer.service';
import { PotentialCustomerRepository } from '@v1/repositories/potential-customer.repository';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import CustomError from '@common/helpers/custom-error.helper';
import { controller, httpGet, httpPost, httpPut, httpDelete, request, response, httpPatch, requestParam } from 'inversify-express-utils';
import TYPES from '@common/types/inversify.types';
import { inject } from 'inversify';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import { PotentialCustomerSchema } from '@v1/dtos/potential-customer.dtos';
import { merchantIdFilter, appSearch } from '@v1/dtos/common.dtos';
import { UserRole } from '@common/enums/index.enums';

@controller('', Auth.validateAuth([UserRole.MERCHANT]))
class PotentialCustomerController {
  private potentialCustomerRepository;
  private potentialCustomerService;

  constructor(
    @inject(TYPES.PotentialCustomerRepository) potentialCustomerRepository: PotentialCustomerRepository,
    @inject(TYPES.PotentialCustomerService) potentialCustomerService: PotentialCustomerService,
  ) {
    this.potentialCustomerRepository = potentialCustomerRepository;
    this.potentialCustomerService = potentialCustomerService;
  }

  @httpPost('/potential-customers')
  public async createPotentialCustomers(@request() req: express.Request, @response() res: CustomResponse) {
    let statusCode: number;
    let status: boolean = true;
    try {
      const { successData, errorData } = await this.potentialCustomerService.createPotentialCustomers({
        merchantId: res.locals.jwt._id,
        ...req.body,
      });
      const modifiedErrorData = errorData.map(error => ({ data: error.data, errorMessage: error.errorMessage }));
      const data = { successData: successData.length ? successData : null, errorData: modifiedErrorData.length ? modifiedErrorData : null };
      const message = successData.length
        ? `${successData.length} Merchant potential customer${successData.length > 1 ? 's' : ''} created successfully`
        : `Merchant potential customer${errorData.length > 1 ? 's' : ''} could not be created`;
      if (successData.length && errorData.length) {
        statusCode = 207;
      } else if (successData.length && !errorData.length) {
        statusCode = 201;
      } else {
        const errorMessages = errorData.map(data => data.statusCode);
        const uniqueErrorMessages = [...new Set(errorMessages)];
        statusCode = uniqueErrorMessages[0];
        status = false;
      }
      res.handleSuccessResponse(statusCode, message, data, status);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/potential-customer', validate(PotentialCustomerSchema.update))
  public async updatePotentialCustomerById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.potentialCustomerService.updatePotentialCustomerById(req.body);

      if (!potentialCustomer) {
        throw new CustomError(404, 'Potential customer not found');
      }

      res.handleSuccessResponse(200, `Potential customer updated`, potentialCustomer);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/merchant/:merchant_id/potential-customers', validate(merchantIdFilter))
  public async getPotentialCustomerByMerchantId(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.potentialCustomerService.getPotentialCustomersByMerchantId(req.params.merchant_id, req.query);
      res.handleSuccessResponse(200, `Merchant's potential customer retrieved`, potentialCustomer);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/potential-customer/:potential_customer_id', validate(PotentialCustomerSchema.potentialCustomerId))
  public async getPotentialCustomerById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.potentialCustomerRepository.getPotentialCustomerById(req.params.potential_customer_id);

      if (!potentialCustomer) {
        throw new CustomError(404, 'Potential customer not found');
      }

      res.handleSuccessResponse(200, `Potential customer retrieved`, potentialCustomer);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete('/potential-customer/:potential_customer_id', validate(PotentialCustomerSchema.potentialCustomerId))
  public async deletePotentialCustomerById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.potentialCustomerRepository.updateDeleteStatus(req.params.potential_customer_id);

      if (!potentialCustomer) {
        throw new CustomError(404, 'Potential customer not found');
      }

      res.handleSuccessResponse(200, 'Potential customer deleted');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/merchant/:merchant_id/potential-customers/search', validate(appSearch))
  public async search(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomers = await this.potentialCustomerService.searchByMerchantId(req.params.merchant_id, req.query);

      res.handleSuccessResponse(200, `Merchant's potential customers retrieved`, potentialCustomers);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
