import TYPES from '@common/types/inversify.types';
import { SubscriptionPlanRepository } from '@v1/repositories/subscription-plan.repository';
import { inject, injectable } from 'inversify';

@injectable()
class SubscriptionPlanService {
  constructor(@inject(TYPES.SubscriptionPlanRepository) private subscriptionPlanRepository: SubscriptionPlanRepository) {}

  async getSubscriptionPlans() {
    return await this.subscriptionPlanRepository.getSubscriptionPlans()
  }

}

export { SubscriptionPlanService };

