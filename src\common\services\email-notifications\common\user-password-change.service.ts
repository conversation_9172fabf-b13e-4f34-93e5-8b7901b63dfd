import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class UserPasswordChangeMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string; code: string }): Promise<EmailNotifier> {
    this.emailNotifier.setMailProperties('Password change', 'wadoo.user.password_change', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default UserPasswordChangeMail;
