import config from '@common/config/env.config';
import { AuthTokenType } from '@common/enums/index.enums';
import crypto from 'crypto';
import { injectable } from 'inversify';
import 'reflect-metadata';
import { AuthToken } from '../../common/schema/index.schema';
import BaseRepository from './base.repository';

@injectable()
class AuthTokenRepository extends BaseRepository {
  constructor() {
    super(AuthToken);
  }

  async createAuthToken(user: any, type: AuthTokenType) {
    const refreshId = user._id + config.REFRESH_TOKEN_SECRET_KEY;
    const salt = crypto.createSecretKey(crypto.randomBytes(16));

    const refreshToken = crypto.createHmac('sha512', salt).update(refreshId).digest('base64');

    const refreshKey = salt.export();
    const tokenExpiryDate = new Date();
    tokenExpiryDate.setDate(tokenExpiryDate.getDate() + 1);

    const resource = {
      user: user._id,
      token: refreshToken,
      key: refreshKey,
      type,
      expiry: tokenExpiryDate,
    };

    return await this.create(resource);
  }

  async getToken(refreshToken: string) {
    return await AuthToken.findOne({ token: refreshToken }).exec();
  }

  async getAuthTokenByUserId(userId: string) {
    return await AuthToken.findOne({ user: userId }).sort('-createdAt').exec();
  }

  async deleteExpiredAuthTokens() {
    const date = new Date();
    await AuthToken.deleteMany({ expiry: { $lte: date } }).exec();
  }
}

export { AuthTokenRepository };

export default new AuthTokenRepository();
