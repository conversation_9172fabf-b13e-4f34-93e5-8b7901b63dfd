import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';
import { ProcessResponse } from '@common/types/common.types';

@injectable()
class AdminWelcomeMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string }): Promise<any> {
    this.emailNotifier.setMailProperties('Welcome on board!', 'wadoo.admin.welcome', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default AdminWelcomeMail;
