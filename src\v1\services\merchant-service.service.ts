import cloudinary from '@common/config/cloudinary.config';
import { ServiceDefaultSetting } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { Image } from '@common/types/common.types';
import TYPES from '@common/types/inversify.types';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import { injectable, inject } from 'inversify';
import mongoose from 'mongoose';
import 'reflect-metadata';

@injectable()
class MerchantService {
  constructor(
    @inject(TYPES.ServiceRepository) private serviceRepository: ServiceRepository,
    @inject(TYPES.MerchantAccountRepository) private merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
  ) {}

  async getMerchantServices(merchantId: string) {
    return await this.serviceRepository.getMerchantServices(merchantId);
  }

  async getMerchantService(resource: any) {
    const merchantService = await this.serviceRepository.getMerchantService(resource);
    if (!merchantService) {
      throw new CustomError(404, 'Service not found');
    }
    return merchantService;
  }
  async getServiceDetails(service: any) {
    const serviceDetails: any = {};

    Object.keys(service).forEach((key: string) => {
      if (key === 'serviceId') {
        serviceDetails['_id'] = service.serviceId;
      } else if (key === 'merchantId') {
        serviceDetails['user'] = service[key];
      } else {
        serviceDetails[key] = service[key];
      }
    });

    return serviceDetails;
  }

  async getModifiedService(service: any) {
    const merchant = await this.merchantAccountRepository.getByUserId(service.user);

    return {
      _id: service._id,
      merchant: {
        businessName: merchant?.businessName || null,
      },
      name: service.name,
      duration: service.duration,
      description: service.description,
      price: service.price,
      isNegotiable: service.isNegotiable,
      reminderFrequency: service.reminderFrequency,
      reviewImage: service.reviewImage,
      createdAt: service.createdAt,
      updatedAt: service.updatedAt,
    };
  }

  async uploadReviewImage(path: string) {
    try {
      const picture = await cloudinary.uploader.upload(path, {
        folder: 'services',
      });

      return {
        id: picture.public_id,
        url: picture.secure_url,
      };
    } catch (error) {
      throw new CustomError(500, 'Problem uploading review image');
    }
  }

  async updateReviewImage(resource: any) {
    const { merchantId, serviceId, file } = resource;
    let reviewImage: Image;

    const service: any = await this.serviceRepository.getMerchantService({ merchantId, serviceId });

    if (!service) {
      throw new CustomError(404, 'Service not found');
    }

    if (service.reviewImage.id && service.reviewImage.url) {
      await cloudinary.uploader.destroy(service.reviewImage.id);
    }

    if (file) {
      reviewImage = await this.uploadReviewImage(file.path);
    } else {
      reviewImage = {
        id: null,
        url: ServiceDefaultSetting.reviewImageUrl,
      };
    }

    return await this.serviceRepository.updateServiceById({ _id: serviceId, reviewImage });
  }

  async createService(resource: any, file: any = null) {
    const user = await this.userRepository.getById(resource.merchantId);

    resource.merchant = user.merchant;

    if (file) {
      resource.reviewImage = await this.uploadReviewImage(file.path);
    }

    if (typeof resource.isNegotiable === 'string' && resource.isNegotiable.toLowerCase() === 'true') {
      resource.isNegotiable = true;
    } else if (typeof resource.isNegotiable === 'string' && resource.isNegotiable.toLowerCase() !== 'true') {
      resource.isNegotiable = false;
    }

    const serviceDetails = await this.getServiceDetails(resource);

    const service = await this.serviceRepository.create(serviceDetails);

    const modifiedService = await this.getModifiedService(service);

    return modifiedService;
  }

  async updateMerchantService(resource: any) {
    const { merchantId, serviceId } = resource;
    const serviceDetails = await this.getServiceDetails(resource);
    if (!mongoose.Types.ObjectId.isValid(serviceId)) {
      throw new CustomError(404, 'Service not found');
    }
    const merchantService = await this.serviceRepository.getMerchantService({ merchantId, serviceId });

    if (!merchantService) {
      throw new CustomError(404, 'Service not found');
    }

    const service = await this.serviceRepository.updateServiceById(serviceDetails);

    if (!service) {
      throw new CustomError(404, 'Service not found');
    }

    return service;
  }

  async suspendService(serviceId: string) {
    return await this.serviceRepository.update({ _id: serviceId, isSuspended: true });
  }

  async getServicesByMerchantId(merchantId: string, resource: any) {
    const merchant = await this.merchantAccountRepository.getByUserId(merchantId);

    if (!merchant) {
      throw new CustomError(404, 'Merchant not found');
    }

    return await this.serviceRepository.getServicesByMerchantId(merchantId, resource.page, resource?.limit, resource?.startDate, resource?.endDate);
  }

  async deleteMerchantService(id: string, userId: string | null = null) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new CustomError(404, 'Service not found');
    }

    if (!(await this.serviceRepository.updateDeleteStatus(id, userId))) {
      throw new CustomError(404, 'Service not found');
    }
  }

}

export { MerchantService };
