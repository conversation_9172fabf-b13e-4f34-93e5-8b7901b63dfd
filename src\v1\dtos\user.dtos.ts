import { z as schema } from 'zod';

const userId = schema.object({
  params: schema
    .object({
      user_id: schema.string(),
    })
    .strict(),
});

const updateUser = schema.object({
  body: schema
    .object({
      userId: schema.string(),
      firstName: schema.string().optional(),
      lastName: schema.string().optional(),
      phoneNumber: schema.string().optional(),
    })
    .strict(),
});

export const UserSchema = {
  userId,
  updateUser,
};
