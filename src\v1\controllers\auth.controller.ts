import express from 'express';
import { controller, httpGet, httpPost, httpPut, httpDelete, request, response, httpPatch, requestParam } from 'inversify-express-utils';
import { inject } from 'inversify';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import { AuthService } from '../services/auth.service';
import TYPES from '@common/types/inversify.types';
import { validate } from '@common/middleware/validate.middleware';
import { AuthSchema } from '@v1/dtos/auth.dtos';
import Auth from '@common/middleware/auth.middleware';

@controller('/auth')
class AuthController {
  private authService;

  constructor(@inject(TYPES.AuthService) authService: AuthService) {
    this.authService = authService;
  }

  @httpPost('/sign-up', validate(AuthSchema.merchantSignUp))
  public async merchantSignUp(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.authService.merchantSignUp(req.body);
      res.handleSuccessResponse(201, 'Verification OTP sent to merchant');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
  @httpPost('/sign-in', validate(AuthSchema.signIn), Auth.validateUser)
  public async signIn(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const userData = await this.authService.signIn(req.body);
      res.handleSuccessResponse(200, 'Login successful', userData);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/sign-out', Auth.validateAuth())
  public async signOut(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const userData = await this.authService.signOut();
      res.handleSuccessResponse(200, 'Logout successful', userData);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/refresh-token', validate(AuthSchema.refreshToken), Auth.validateRefreshToken)
  async refreshToken(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const userData = await this.authService.refreshAuthenticationToken(req.body);
      res.handleSuccessResponse(200, 'Token refresh successful', userData);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/verify-account', validate(AuthSchema.verifyAccount))
  public async verifyAccount(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.authService.verifyAccount(req.body);
      res.handleSuccessResponse(200, 'Account verified', data);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/account-verification-code', validate(AuthSchema.email))
  public async sendAccountVerificationOtp(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.authService.sendAccountVerificationOtp(req.body.email);
      res.handleSuccessResponse(200, 'Account verification code sent', data);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/forgot-password', validate(AuthSchema.email), Auth.validateUser)
  public async sendPasswordResetOtp(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.authService.sendPasswordResetOtp(req.body.email);
      res.handleSuccessResponse(200, 'Password reset code sent', data);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/reset-password', validate(AuthSchema.resetPassword))
  public async resetPassword(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.authService.resetPassword(req.body);
      res.handleSuccessResponse(200, 'Password reset');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/password-change-code', Auth.validateAuth())
  public async sendPasswordChangeOtp(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.authService.sendPasswordChangeOtp(res.locals.jwt.email);
      res.handleSuccessResponse(200, 'Password change code sent', data);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/change-password', Auth.validateAuth(), validate(AuthSchema.changePassword))
  public async changePassword(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.authService.changePassword(res.locals.jwt.email, req.body);
      res.handleSuccessResponse(200, 'Password changed');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/email', Auth.validateAuth())
  public async sendEmailChangeOtp(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.authService.sendEmailChangeRequestOtp(res.locals.jwt.email);
      res.handleSuccessResponse(200, 'Email change code sent');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/change-email', Auth.validateAuth(), validate(AuthSchema.changeEmail))
  public async changeUserEmail(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.authService.changeEmail(res.locals.jwt.email, req.body);
      res.handleSuccessResponse(200, 'Email updated', data);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
}

export { AuthController };
