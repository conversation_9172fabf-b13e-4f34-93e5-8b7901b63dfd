import { z } from 'zod';
import { SubscriptionPlanSchema } from '@v1/dtos/admin/subscription-plan.dtos';

export type CreateSubscriptionPlan = z.infer<typeof SubscriptionPlanSchema.createSubscriptionPlan>['body'];

export type UpdateSubscriptionPlan = z.infer<typeof SubscriptionPlanSchema.updateSubscriptionPlan>['body'];

export type SuccessData = {
  _id: string;
  name: string;
  amountPerMonth: string;
  description: string;
  createdAt: Date;
  updatedAt: Date;
};

export type ErrorData = {
  subscriptionPlanId: string;
  errorMessage: string;
};
