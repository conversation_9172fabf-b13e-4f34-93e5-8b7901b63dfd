import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class SendAdminInviteMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { userId: string }): Promise<any> {
    this.emailNotifier.setMailProperties('Admin invite', 'wadoo.admin.invite', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default SendAdminInviteMail;
