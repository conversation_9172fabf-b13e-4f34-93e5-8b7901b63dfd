import { UserRole } from '@common/enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { SubscriptionPlanSchema } from '@v1/dtos/admin/subscription-plan.dtos';
import { _idParam } from '@v1/dtos/common.dtos';
import { logAudit } from '@v1/middleware/admin.middleware';
import { AdminSubscriptionPlanService } from '@v1/services/admin/subscription-plan.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpDelete, httpGet, httpPatch, httpPost, request, requestParam, response } from 'inversify-express-utils';

@controller('/admin/subscription-plans')
class AdminSubscriptionPlanController {
  constructor(@inject(TYPES.AdminSubscriptionPlanService) private adminSubscriptionPlanService: AdminSubscriptionPlanService) {}

  @httpGet('/', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]))
  public async getSubscriptionPlans(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const subscriptionPlans = await this.adminSubscriptionPlanService.getSubscriptionPlans();
      res.handleSuccessResponse(200, 'Subscription plans retrieved successfully', subscriptionPlans);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
  @httpPost(
    '/',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(SubscriptionPlanSchema.createSubscriptionPlan),
    logAudit('Subscription plan creation'),
  )
  public async createSubscriptionPlans(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const plan = await this.adminSubscriptionPlanService.createSubscriptionPlan(req.body);
      res.handleSuccessResponse(201, 'Subscription plan created successfully', plan);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(SubscriptionPlanSchema.updateSubscriptionPlan),
    logAudit('Subscription plan update'),
  )
  public async updateSubscriptionPlans(@request() req: express.Request, @response() res: CustomResponse) {
    let statusCode: number;
    let status: boolean = true;
    try {
      const { successData, errorData } = await this.adminSubscriptionPlanService.updateSubscriptionPlans(req.body);
      const data = { successData: successData.length ? successData : null, errorData: errorData.length ? errorData : null };
      const message = successData.length
        ? `${successData.length} Subscription plan${successData.length > 1 ? 's' : ''} updated successfully`
        : ` Subscription plan${errorData.length > 1 ? 's' : ''} could not be updated`;
      if (successData.length && errorData.length) {
        statusCode = 207;
      } else if (successData.length && !errorData.length) {
        statusCode = 200;
      } else {
        const errorMessages = errorData.map(data => data.errorMessage);
        const uniqueErrorMessages = [...new Set(errorMessages)];
        statusCode = uniqueErrorMessages.length > 1 ? 207 : uniqueErrorMessages[0].includes('already exists') ? 409 : 404;
        status = false;
      }
      res.handleSuccessResponse(statusCode, message, data, status);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete(
    '/:subscription_plan_id',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(SubscriptionPlanSchema.subscriptionPlanId),
    logAudit('Subscription plan deletion'),
  )
  public async deleteSubscriptionPlan(@requestParam('subscription_plan_id') planId: string, @response() res: CustomResponse) {
    try {
      await this.adminSubscriptionPlanService.deleteSubscriptionPlan(planId);
      res.handleSuccessResponse(200, 'Subscription plan deleted successfully');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
}
