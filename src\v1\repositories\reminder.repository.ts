import { Reminder } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class ReminderRepository extends BaseRepository {
  constructor() {
    super(<PERSON>minder);
  }

  async deleteByOrderId(orderId: string) {
    return await Reminder.deleteMany({ order: orderId }).exec();
  }

  async getRemindersByMerchantId(merchantId: string) {
    const date = new Date();
    date.setHours(date.getHours() + 1); // to account for the lag in UTC Time Zone
    let startDate: string = date.toISOString().split('T')[0];
    date.setDate(date.getDate() + 1);
    let endDate: string = date.toISOString().split('T')[0];

    return await Reminder.find({ user: merchantId, date: { $gte: startDate, $lt: endDate } }, '-__v -_id -date -frequency -user -order')
      .sort('-createdAt')
      .exec();
  }

  async getDueReminders() {
    const date = new Date();
    date.setHours(date.getHours() - 3);

    return await Reminder.find({ date: { $lte: date } }).exec();
  }

  async updateReminder(reminder: any) {
    const newDate = reminder.date;

    newDate.setDate(newDate.getDate() + reminder.frequency * 7); // reminder frequency is in weeks

    await Reminder.findOneAndUpdate({ _id: reminder._id }, { $set: { date: newDate } }).exec();
  }
}

export { ReminderRepository };
