import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { dashboardSearch, genericFilter } from '@v1/dtos/common.dtos';
import { PotentialCustomerSchema } from '@v1/dtos/potential-customer.dtos';
import { AdminPotentialCustomerService } from '@v1/services/admin/potential-customer.service';
import { PotentialCustomerService } from '@v1/services/potential-customer.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, httpPatch, httpPost, request, response } from 'inversify-express-utils';

@controller('/admin')
class AdminPotentialCustomerController {
  constructor(@inject(TYPES.AdminPotentialCustomerService) private adminPotentialCustomerService: AdminPotentialCustomerService) {}

  @httpPost(
    '/potential-customers',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
  )
  public async createPotentialCustomers(@request() req: express.Request, @response() res: CustomResponse) {
    let statusCode: number;
    let status: boolean = true;
    try {
      const { successData, errorData } = await this.adminPotentialCustomerService.createPotentialCustomers(req.body);
      const modifiedErrorData = errorData.map(error => ({ data: error.data, errorMessage: error.errorMessage }));
      const data = { successData: successData.length ? successData : null, errorData: modifiedErrorData.length ? modifiedErrorData : null };
      const message = successData.length
        ? `${successData.length} Merchant potential customer${successData.length > 1 ? 's' : ''} created successfully`
        : `Merchant potential customer${errorData.length > 1 ? 's' : ''} could not be created`;
      if (successData.length && errorData.length) {
        statusCode = 207;
      } else if (successData.length && !errorData.length) {
        statusCode = 201;
      } else {
        const errorMessages = errorData.map(data => data.statusCode);
        const uniqueErrorMessages = [...new Set(errorMessages)];
        statusCode = uniqueErrorMessages[0];
        status = false;
      }
      res.handleSuccessResponse(statusCode, message, data, status);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/potential-customer', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]), validate(PotentialCustomerSchema.update))
  public async updatePotentialCustomerById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.adminPotentialCustomerService.updatePotentialCustomerById(req.body);

      if (!potentialCustomer) {
        throw new CustomError(404, 'Potential customer not found');
      }

      res.handleSuccessResponse(200, `Potential customer updated`, potentialCustomer);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet(
    '/potential-customer/:potential_customer_id',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    validate(PotentialCustomerSchema.potentialCustomerId),
  )
  public async getPotentialCustomerById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.adminPotentialCustomerService.getPotentialCustomerById(req.params.potential_customer_id);

      res.handleSuccessResponse(200, `Potential customer retrieved`, potentialCustomer);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/potential-customers', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(genericFilter))
  public async getPotentialCustomers(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomer = await this.adminPotentialCustomerService.getPotentialCustomers(req.query);

      res.handleSuccessResponse(200, 'Potential customers retrieved', potentialCustomer);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/potential-customers/search', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(dashboardSearch))
  public async search(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const potentialCustomers = await this.adminPotentialCustomerService.search(req.query);

      res.handleSuccessResponse(200, `Potential customers retrieved`, potentialCustomers);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
