import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, httpPost, request, response } from 'inversify-express-utils';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import { VirtualAccountSchema } from '@v1/dtos/virtual-account.dtos';
import { WalletService } from '@v1/services/wallet.service';

@controller('')
class VirtualAccountController {
  private wallet;

  constructor(@inject(TYPES.WalletService) wallet: WalletService) {
    this.wallet = wallet;
  }

  @httpPost('/virtual-account', Auth.validateAuth(), validate(VirtualAccountSchema.createWallet))
  public async createVirtualAccount(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const response = await this.wallet.createWallet(req.body);

      if (response.error) {
        res.handleErrorResponse(response.data);
      } else {
        res.handleSuccessResponse(200, 'Virtual account created', response.data);
      }
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/virtual-account/:wallet_id', Auth.validateAuth(), validate(VirtualAccountSchema.fetchWallet))
  public async fetchWallet(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const response = await this.wallet.fetchWallet(req.params.wallet_id);

      if (response.error) {
        res.handleErrorResponse(response.data);
      } else {
        res.handleSuccessResponse(200, 'Wallet retrieved', response.data);
      }
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/virtual-account/:wallet_id/funds/transfer', Auth.validateAuth(), validate(VirtualAccountSchema.transferWalletFunds))
  public async transferWalletFunds(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const response = await this.wallet.transferFundsFromMerchantWallet(req.params.wallet_id, req.body);

      if (response.error) {
        res.handleErrorResponse(response.data);
      } else {
        res.handleSuccessResponse(200, 'Funds transferred', response.data);
      }
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/virtual-account/:wallet_id/statement', Auth.validateAuth(), validate(VirtualAccountSchema.fetchWalletTransactions))
  public async fetchWalletTransactions(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const response = await this.wallet.fetchWalletTransactions(req.params.wallet_id, req.query);

      if (response.error) {
        res.handleErrorResponse(response.data);
      } else {
        res.handleSuccessResponse(200, 'Wallet statement retrieved', response.data);
      }
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/commercial-banks', Auth.validateAuth())
  public async listOfBanks(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const response = await this.wallet.fetchListOfBanks();

      if (response.error) {
        res.handleErrorResponse(response.data);
      } else {
        res.handleSuccessResponse(200, 'Wallet retrieved', response.data);
      }
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
