import cloudinary from '@common/config/cloudinary.config';
import { ServiceDefaultSetting, ServiceStatus, SubscriptionStatus } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { Image } from '@common/types/common.types';
import TYPES from '@common/types/inversify.types';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import { MerchantService } from '@v1/services/merchant-service.service';
import { inject, injectable } from 'inversify';
import mongoose from 'mongoose';
import 'reflect-metadata';

@injectable()
class AdminDashboardService {
  constructor(
    @inject(TYPES.ServiceRepository) private serviceRepository: ServiceRepository,
    @inject(TYPES.MerchantAccountRepository) private merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
    @inject(TYPES.MerchantService) private merchantService: MerchantService,
  ) {}

  async getServices(resource: any) {
    return await this.serviceRepository.getServices(resource.page, resource?.limit, resource?.startDate, resource?.endDate);
  }

  async getServiceById(serviceId: string) {
    const service = await this.serviceRepository.getService(serviceId);
    if (!service) {
      throw new CustomError(404, 'No service found');
    }
    return service;
  }

  async createService(resource: any, file: any = null) {
    if (!mongoose.Types.ObjectId.isValid(resource.merchantId)) {
      throw new CustomError(404, 'Merchant not found');
    }

    const user = await this.userRepository.getById(resource.merchantId);

    if (!user) {
      throw new CustomError(404, 'Merchant not found');
    }

    resource.merchant = user.merchant;

    if (file) {
      resource.reviewImage = await this.merchantService.uploadReviewImage(file.path);
    }

    if (typeof resource.isNegotiable === 'string' && resource.isNegotiable.toLowerCase() === 'true') {
      resource.isNegotiable = true;
    } else if (typeof resource.isNegotiable === 'string' && resource.isNegotiable.toLowerCase() !== 'true') {
      resource.isNegotiable = false;
    }

    const serviceDetails = await this.merchantService.getServiceDetails(resource);

    const service = await this.serviceRepository.create(serviceDetails);

    const modifiedService = await this.merchantService.getModifiedService(service);

    return modifiedService;
  }

  async updateServiceById(resource: any) {
    const serviceDetails = await this.merchantService.getServiceDetails(resource);
    const service = await this.serviceRepository.updateServiceById(serviceDetails);

    if (!service) {
      throw new CustomError(404, 'Service not found');
    }

    return service;
  }

  async updateReviewImage(id: string, file: any) {
    let reviewImage: Image;
    const service: any = await this.serviceRepository.getService(id);

    if (!service) {
      throw new CustomError(404, 'Service not found');
    }

    if (service.reviewImage.id && service.reviewImage.url) {
      await cloudinary.uploader.destroy(service.reviewImage.id);
    }

    if (file) {
      reviewImage = await this.merchantService.uploadReviewImage(file.path);
    } else {
      reviewImage = {
        id: null,
        url: ServiceDefaultSetting.reviewImageUrl,
      };
    }

    return await this.serviceRepository.updateServiceById({ _id: id, reviewImage });
  }

  async updateDeleteStatus(serviceId: string) {
    if (!mongoose.Types.ObjectId.isValid(serviceId) || !(await this.serviceRepository.updateDeleteStatus(serviceId))) {
      throw new CustomError(404, 'Service not found');
    }
  }

  async search(resource: any) {
    return await this.serviceRepository.search(resource.page, resource?.limit, resource.word);
  }

  async searchByMerchantId(merchantId: string, resource: any) {
    return await this.serviceRepository.searchByMerchantId(merchantId, resource.page, resource?.limit, resource.word);
  }

  async toggleServiceSuspension(_id: string, isSuspension: boolean = false) {
    if (!mongoose.Types.ObjectId.isValid(_id)) {
      throw new CustomError(404, 'Service not found');
    }

    const service = await this.serviceRepository.getService(_id);

    if (!service) {
      throw new CustomError(404, 'Service not found');
    }

    if (isSuspension && service.status === ServiceStatus.SUSPENDED) {
      throw new CustomError(409, 'Service is already suspended!');
    }

    if (!isSuspension && service.status === ServiceStatus.ACTIVE) {
      throw new CustomError(409, 'Service is already active!');
    }

    const updatedService = await this.serviceRepository.updateServiceById({
      _id,
      status: isSuspension ? SubscriptionStatus.SUSPENDED : SubscriptionStatus.ACTIVE,
    });

    return updatedService;
  }
}

export { AdminDashboardService };

