import { injectable } from 'inversify';
import cron from 'node-cron';
import fs from 'fs';
import logger from '@common/logger';

@injectable()
class PictureUploadService {
  async emptyUploadFolder() {
    cron.schedule(
      `* 2 * * *`,
      async () => {
        const directory = 'uploads/';

        fs.readdir(directory, (err, files) => {
          files?.forEach(file => {
            fs.unlink(directory + file, err => {
              if (err) {
                logger.error('Uploads folder clean up is unsuccessful');
              }
            });
          });
        });
      },
      {
        scheduled: true,
        timezone: 'Africa/Lagos',
      },
    );
  }
}

export { PictureUploadService };
