import { UserRole } from '@common/enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import UploadMiddleware from '@common/middleware/upload.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { AdminServiceSchema } from '@v1/dtos/admin/service.dtos';
import { dashboardSearch, genericFilter } from '@v1/dtos/common.dtos';
import { ServiceSchema } from '@v1/dtos/service.dtos';
import { logAudit } from '@v1/middleware/admin.middleware';
import { AdminDashboardService } from '@v1/services/admin/merchant-service.service';
import { MerchantService } from '@v1/services/merchant-service.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpDelete, httpGet, httpPatch, httpPost, request, response } from 'inversify-express-utils';

@controller('/admin')
export class AdminServiceController {
  constructor(
    @inject(TYPES.MerchantService) private merchantService: MerchantService,
    @inject(TYPES.AdminDashboardService) private adminDashboardService: AdminDashboardService,
  ) {}

  @httpPost(
    '/services',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    UploadMiddleware.uploadReviewImage(),
    validate(AdminServiceSchema.createService),
    logAudit('Service creation'),
  )
  public async createService(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.adminDashboardService.createService(req.body, req.file);
      res.handleSuccessResponse(201, 'Service created', service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/services/review-image',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    UploadMiddleware.uploadReviewImage(),
    validate(ServiceSchema.bodyId),
    logAudit('Service update'),
  )
  public async updateReviewImage(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.adminDashboardService.updateReviewImage(req.body.serviceId, req.file);
      res.handleSuccessResponse(200, `Review image updated`, service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/services', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(genericFilter))
  public async getServices(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const services = await this.adminDashboardService.getServices(req.query);

      res.handleSuccessResponse(200, 'Services retrieved', services);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/services/search', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(dashboardSearch))
  public async search(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const services = await this.adminDashboardService.search(req.query);
      res.handleSuccessResponse(200, `Services retrieved`, services);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet(
    '/services/:service_id',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    validate(AdminServiceSchema.serviceId),
  )
  public async getServiceById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.adminDashboardService.getServiceById(req.params.service_id);

      res.handleSuccessResponse(200, `Service retrieved`, service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/services',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminServiceSchema.updateService),
    logAudit('Service update'),
  )
  public async updateServiceById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.adminDashboardService.updateServiceById(req.body);
      res.handleSuccessResponse(200, `Service updated`, service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/merchant/:merchant_id/services', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(genericFilter))
  public async getServicesByMerchantId(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const services = await this.merchantService.getServicesByMerchantId(req.params.merchant_id, req.query);
      res.handleSuccessResponse(200, `Merchant's services retrieved`, services);
    } catch (error: any) {}
  }

  @httpDelete(
    '/services/:service_id',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminServiceSchema.serviceId),
    logAudit('Service deletion'),
  )
  public async deleteServiceById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.adminDashboardService.updateDeleteStatus(req.params.service_id);
      res.handleSuccessResponse(200, 'Service deleted');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet(
    '/merchant/:merchant_id/services/search',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    validate(dashboardSearch),
  )
  public async searchByMerchantId(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const services = await this.adminDashboardService.searchByMerchantId(req.params.merchant_id, req.query);

      res.handleSuccessResponse(200, `Merchant's services retrieved`, services);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/services/suspend',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminServiceSchema.bodyId),
    logAudit('Service suspension'),
  )
  public async suspendService(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.adminDashboardService.toggleServiceSuspension(req.body.serviceId, true);
      res.handleSuccessResponse(200, 'Service suspended');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/services/unsuspend',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminServiceSchema.bodyId),
    logAudit('Service suspension'),
  )
  public async unsuspendService(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.adminDashboardService.toggleServiceSuspension(req.body.serviceId);
      res.handleSuccessResponse(200, 'Service unsuspended');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
