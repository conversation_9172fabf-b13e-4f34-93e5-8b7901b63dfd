import { z as schema } from "zod";

const createMerchant = schema.object({
    body: schema.object({
      firstName: schema.string(),
      lastName: schema.string(),
      businessTypeId: schema.string(),
      businessName: schema.string(),
      email: schema.string().email(),
      phoneNumber: schema.string(),
      password: schema.string(),
      address: schema.string(),
      state: schema.string(),
      openingTime: schema.string(),
      closingTime: schema.string(),
      openingDays: schema.string()
    }).strict()
  });

  const updateMerchant = schema.object({
    body: schema.object({
        merchantId: schema.string(),
        firstName: schema.string().optional(),
        lastName: schema.string().optional(),
        businessTypeId: schema.string().optional(),
        businessName: schema.string().optional(),
        phoneNumber: schema.string().optional(),
        address: schema.string().optional(),
        state: schema.string().optional(),
        openingTime: schema.string().optional(),
        closingTime: schema.string().optional(),
        openingDays: schema.string().optional(),
        twitterHandle: schema.string().optional(),
        instagramHandle: schema.string().optional(),
        facebookUsername: schema.string().optional(),
    }).strict()
});


  export const AdminMerchantSchema = {
    createMerchant,
    updateMerchant,
}