import { MerchantStatus } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { convertNameToSentenceCase } from '@common/helpers/names.helper';
import MerchantWelcomeMail from '@common/services/email-notifications/merchant/welcome.service';
import TYPES from '@common/types/inversify.types';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import { inject, injectable } from 'inversify';
import 'reflect-metadata';

@injectable()
class AdminMerchantService {
  private merchantAccountRepository;
  private serviceRepository;
  private userRepository;
  private merchantWelcomeMail;

  constructor(
    @inject(TYPES.UserRepository) userRepository: UserRepository,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.MerchantWelcomeMail) merchantWelcomeMail: MerchantWelcomeMail,
    @inject(TYPES.ServiceRepository) serviceRepository: ServiceRepository,
  ) {
    this.userRepository = userRepository;
    this.merchantAccountRepository = merchantAccountRepository;
    this.merchantWelcomeMail = merchantWelcomeMail;
    this.serviceRepository = serviceRepository;
  }

  async verifyMerchant(id: string) {
    const merchant = await this.merchantAccountRepository.getByUserId(id);

    if (merchant?.status === MerchantStatus.ACTIVE) {
      throw new CustomError(400, 'Merchant already verified!');
    }

    if (merchant && merchant.status === MerchantStatus.NOT_VERIFIED) {
      const user = await this.userRepository.update({ _id: id, isVerified: true, verifiedAt: new Date() });

      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: id, status: MerchantStatus.ACTIVE });

      await this.merchantWelcomeMail.send(user.email, { name: convertNameToSentenceCase(user.firstName) });

      return updatedMerchant;
    }

    return merchant;
  }

  async toggleMerchantSuspension(id: string, isSuspension: boolean = false) {
    const user = await this.userRepository.getById(id);
    const merchant = await this.merchantAccountRepository.getByUserId(id);

    if (merchant?.status === MerchantStatus.SUSPENDED && isSuspension) {
      throw new CustomError(409, 'Merchant already suspended!');
    }

    if (merchant?.status === MerchantStatus.ACTIVE && !isSuspension) {
      throw new CustomError(409, 'Merchant already active!');
    }

    if (merchant && merchant.status !== MerchantStatus.SUSPENDED && isSuspension) {
      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: merchant.user, status: MerchantStatus.SUSPENDED });

      return updatedMerchant;
    }

    if (merchant && merchant.status === MerchantStatus.SUSPENDED && !isSuspension && user.isVerified === true) {
      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: merchant.user, status: MerchantStatus.ACTIVE });

      return updatedMerchant;
    }

    if (merchant && merchant.status === MerchantStatus.SUSPENDED && !isSuspension && user.isVerified === false) {
      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: merchant.user, status: MerchantStatus.NOT_VERIFIED });

      return updatedMerchant;
    }

    return merchant;
  }

  async toggleMerchantBlockage(id: string, isBlockage: boolean = false) {
    const user = await this.userRepository.getById(id);

    const merchant = await this.merchantAccountRepository.getByUserId(id);

    if (merchant?.status === MerchantStatus.BLOCKED && isBlockage) {
      throw new CustomError(400, 'Merchant already blocked!');
    }

    if (merchant?.status === MerchantStatus.ACTIVE && !isBlockage) {
      throw new CustomError(400, 'Merchant already active!');
    }

    if (merchant && merchant.status !== MerchantStatus.BLOCKED && isBlockage) {
      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: merchant.user, status: MerchantStatus.BLOCKED });

      return updatedMerchant;
    } else if (merchant && merchant.status === MerchantStatus.BLOCKED && !isBlockage && user.isVerified === true) {
      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: merchant.user, status: MerchantStatus.ACTIVE });

      return updatedMerchant;
    } else if (merchant && merchant.status === MerchantStatus.BLOCKED && !isBlockage && user.isVerified === false) {
      const updatedMerchant = await this.merchantAccountRepository.updateByUserId({ userId: merchant.user, status: MerchantStatus.NOT_VERIFIED });

      return updatedMerchant;
    }

    return merchant;
  }

  async getMerchants(resource: any) {
    return await this.merchantAccountRepository.getMerchants(resource.page, resource.limit, resource?.startDate, resource?.endDate, resource?.status);
  }

  async search(resource: any) {
    return await this.merchantAccountRepository.search(resource.page, resource.limit, resource.word);
  }
}

export { AdminMerchantService };
