export const convertInputToLowercase = (userInput: any) => {
  let convertedInput: any = {};
  const keysNotToConvertToLowerCase: string[] = ['role', 'password', 'newPassword', 'currentPassword', 'refreshToken'];
  const keysWithArrayOfKeys: string[] = ['potentialCustomers', 'subscriptionPlans'];

  Object.keys(userInput).forEach((key: any) => {
    if (keysWithArrayOfKeys.includes(key)) {
      convertedInput = { [key]: [] };
      let convertedChildInput: any = {};
      userInput[key].forEach((childInput: any) => {
        Object.keys(childInput).forEach((childKey: string) => {
          convertedChildInput[childKey] = typeof childInput[childKey] === 'string' ? childInput[childKey].toLowerCase() : childInput[childKey];
        });
        convertedInput[key].push(convertedChildInput);
        convertedChildInput = {};
      });
    } else {
      convertedInput[key] =
        typeof userInput[key] === 'string' && !keysNotToConvertToLowerCase.includes(key) ? userInput[key].toLowerCase() : userInput[key];
    }
  });

  return convertedInput;
};
