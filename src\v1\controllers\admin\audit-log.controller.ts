import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { _idParam, dashboardSearch, genericFilter } from '@v1/dtos/common.dtos';
import { AuditLogRepository } from '@v1/repositories/audit-log.repository';
import { AuditLogService } from '@v1/services/admin/audit-log.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, request, response } from 'inversify-express-utils';

@controller('/audit-logs', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]))
class AuditLogController {
  private auditLogService;
  private auditLogRepository;

  constructor(
    @inject(TYPES.AuditLogService) auditLogService: AuditLogService,
    @inject(TYPES.AuditLogRepository) auditLogRepository: AuditLogRepository,
  ) {
    this.auditLogService = auditLogService;
    this.auditLogRepository = auditLogRepository;
  }

  @httpGet('/', validate(genericFilter))
  public async getAuditLogs(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const auditLogs = await this.auditLogService.getAuditLogs(req.query);
      res.handleSuccessResponse(200, 'Audit logs retrieved', auditLogs);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/:_id', validate(_idParam))
  public async getAuditLog(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const auditLog = await this.auditLogRepository.getAuditLog(req.params._id);

      if (!auditLog) {
        throw new CustomError(404, 'Audit log not found');
      }

      res.handleSuccessResponse(200, 'Audit log retrieved', auditLog);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/search', validate(dashboardSearch))
  public async search(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const auditLogs = await this.auditLogService.search(req.query);

      res.handleSuccessResponse(200, `Audit logs retrieved`, auditLogs);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
