import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { validateInput } from '@common/helpers/validate-input.helper';
import TYPES from '@common/types/inversify.types';
import { UploadPotentialCustomerValidation } from '@common/types/potential-customers.types';
import { AdminPotentialCustomerSchema } from '@v1/dtos/admin/admin-potential-customer.dtos';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { PotentialCustomerRepository } from '@v1/repositories/potential-customer.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import { inject, injectable } from 'inversify';
import mongoose from 'mongoose';
import 'reflect-metadata';

@injectable()
class AdminPotentialCustomerService {
  constructor(
    @inject(TYPES.PotentialCustomerRepository) private potentialCustomerRepository: PotentialCustomerRepository,
    @inject(TYPES.MerchantAccountRepository) private merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.ServiceRepository) private serviceRepository: ServiceRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
  ) {}

  async getPotentialCustomerDetails(merchantCustomer: any) {
    const potentialCustomerDetails: any = {};

    Object.keys(merchantCustomer).forEach((key: string) => {
      if (key === 'potentialCustomerId') {
        potentialCustomerDetails['_id'] = merchantCustomer[key];
      } else if (key === 'merchantId') {
        potentialCustomerDetails['user'] = merchantCustomer[key];
      } else if (key === 'serviceId') {
        potentialCustomerDetails['serviceRendered'] = merchantCustomer[key];
      } else {
        potentialCustomerDetails[key] = merchantCustomer[key];
      }
    });

    return potentialCustomerDetails;
  }

  async getModifiedCustomer(customer: any) {
    const merchant = await this.merchantAccountRepository.getByUserId(customer.user);

    let service;

    if (customer.serviceRendered) {
      service = await this.serviceRepository.getById(customer.serviceRendered);
    }

    return {
      _id: customer._id,
      merchant: {
        businessName: merchant?.businessName || null,
      },
      name: customer.name,
      email: customer.email,
      address: customer.address,
      phoneNumber: customer.phoneNumber,
      serviceRendered: {
        name: service?.name || null,
      },
      createdAt: customer.createdAt,
      updatedAt: customer.updatedAt,
    };
  }

  async createPotentialCustomers(resource: any) {
    const successData = [];
    const errorData = [];
    for (const customer of resource.potentialCustomers) {
      const validation: UploadPotentialCustomerValidation = validateInput(AdminPotentialCustomerSchema.createSingle, customer);

      if (!validation.success) {
        errorData.push({ statusCode: 400, data: customer, errorMessage: validation.errorMessage });
        continue;
      }

      if (!mongoose.Types.ObjectId.isValid(customer.merchantId)) {
        errorData.push({ statusCode: 404, data: customer, errorMessage: 'Merchant ID does not exist' });
        continue;
      }

      const user = await this.userRepository.getById(customer.merchantId);
      if (!user || user.role !== UserRole.MERCHANT) {
        errorData.push({ statusCode: 404, data: customer, errorMessage: 'Merchant ID does not exist' });
        continue;
      }

      if (customer.serviceId) {
        if (!mongoose.Types.ObjectId.isValid(customer.serviceId)) {
          errorData.push({ statusCode: 404, data: customer, errorMessage: 'Service ID does not exist' });
          continue;
        }
        const service = await this.serviceRepository.getById(customer.serviceId);
        if (!service) {
          errorData.push({ statusCode: 404, data: customer, errorMessage: 'Service ID does not exist' });
          continue;
        }
        if (String(service.user) !== customer.merchantId) {
          errorData.push({ statusCode: 403, data: customer, errorMessage: 'Service does not belong to this merchant' });
          continue;
        }
      }

      customer.merchant = user.merchant;
      const potentialCustomerDetails = await this.getPotentialCustomerDetails(customer);
      const createdCustomer = await this.potentialCustomerRepository.create(potentialCustomerDetails);
      const modifiedCustomer = await this.getModifiedCustomer(createdCustomer);
      successData.push(modifiedCustomer);
    }
    return { successData, errorData };
  }

  async getPotentialCustomerById(customerId: string) {
    const potentialCustomer = await this.potentialCustomerRepository.getPotentialCustomerById(customerId);

    if (!potentialCustomer) {
      throw new CustomError(404, 'Potential customer not found');
    }

    return potentialCustomer;
  }

  async updatePotentialCustomerById(resource: any) {
    const potentialCustomerDetails = await this.getPotentialCustomerDetails(resource);
    const potentialCustomer = await this.potentialCustomerRepository.updatePotentialCustomerById(potentialCustomerDetails);

    if (!potentialCustomer) {
      throw new CustomError(404, 'Potential customer not found');
    }

    return potentialCustomer;
  }

  async getPotentialCustomers(resource: any) {
    return await this.potentialCustomerRepository.getPotentialCustomers(resource.page, resource.limit, resource?.startDate, resource?.endDate);
  }

  async search(resource: any) {
    return await this.potentialCustomerRepository.search(resource.page, resource?.limit, resource.word);
  }
}

export { AdminPotentialCustomerService };

