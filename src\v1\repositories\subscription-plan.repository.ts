import { SubscriptionPlan } from '@common/schema/index.schema';
import BaseRepository from './base.repository';
import { injectable } from 'inversify';
import { UpdateSubscriptionPlan } from '@common/types/subscription-plan.types';

@injectable()
class SubscriptionPlanRepository extends BaseRepository {
  constructor() {
    super(SubscriptionPlan);
  }

  async getSubscriptionPlanByName(name: string) {
    return await SubscriptionPlan.findOne({ name }).exec();
  }

  async getSubscriptionPlanById(id: string) {
    return await SubscriptionPlan.findById(id).exec();
  }

  async getSubscriptionPlans() {
    return await SubscriptionPlan.find({ isDeleted: false }).select('-__v').sort('amountPerMonth').exec();
  }

  async updateSubscriptionPlans(resource: UpdateSubscriptionPlan['subscriptionPlans'][0]) {
    const { subscriptionPlanId, ...data } = resource;
    return await SubscriptionPlan.findByIdAndUpdate(subscriptionPlanId, data, { new: true }).select('-__v').exec();
  }
}

export { SubscriptionPlanRepository };
