import { z as schema } from 'zod';

const createWallet = schema.object({
  body: schema
    .object({
      merchantId: schema.string(),
      firstNameOnBvn: schema.string(),
      lastNameOnBvn: schema.string(),
      dateOfBirthOnBvn: schema.string(),
      phoneNumberOnBvn: schema.string(),
      emailOnBvn: schema.string().email(),
      genderOnBvn: schema.string(),
      addressOnBvn: schema.string(),
      stateOnBvn: schema.string(),
      countryOnBvn: schema.string(),
      bvn: schema.string(),
    })
    .strict(),
});

const fetchWallet = schema.object({
  params: schema
    .object({
      wallet_id: schema.string(),
    })
    .strict(),
});

const transferWalletFunds = schema.object({
  body: schema
    .object({
      amount: schema.number(),
      beneficiaryBankSortCode: schema.string(),
      beneficiaryAccountNumber: schema.string(),
      narration: schema.string(),
    })
    .strict(),
  params: schema
    .object({
      wallet_id: schema.string(),
    })
    .strict(),
});

const fetchWalletTransactions = schema.object({
  query: schema
    .object({
      startDate: schema.string(),
      endDate: schema.string(),
      limit: schema.string(),
    })
    .strict(),
  params: schema
    .object({
      wallet_id: schema.string(),
    })
    .strict(),
});

export const VirtualAccountSchema = {
  createWallet,
  fetchWallet,
  transferWalletFunds,
  fetchWalletTransactions,
};
