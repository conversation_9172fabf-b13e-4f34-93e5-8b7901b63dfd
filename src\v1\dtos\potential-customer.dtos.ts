import { z as schema } from 'zod';

const create = schema.object({
  body: schema
    .object({
      potentialCustomers: schema
        .object({
          merchantId: schema.string(),
          name: schema.string(),
          email: schema.string().email().optional(),
          phoneNumber: schema.string(),
          address: schema.string().optional(),
          serviceId: schema.string().optional(),
        })
        .array()
        .nonempty(),
    })
    .strict(),
});

const createSingle = schema
  .object({
    name: schema.string(),
    email: schema.string().email().optional(),
    phoneNumber: schema.string(),
    address: schema.string().optional(),
    serviceId: schema.string().optional(),
  })
  .strict();

const update = schema.object({
  body: schema
    .object({
      potentialCustomerId: schema.string(),
      name: schema.string().optional(),
      email: schema.string().email().optional(),
      phoneNumber: schema.string().optional(),
      address: schema.string().optional(),
      serviceId: schema.string().optional(),
    })
    .strict(),
});

const potentialCustomerId = schema.object({
  params: schema
    .object({
      potential_customer_id: schema.string(),
    })
    .strict(),
});

export const PotentialCustomerSchema = {
  potentialCustomerId,
  create,
  createSingle,
  update,
};
