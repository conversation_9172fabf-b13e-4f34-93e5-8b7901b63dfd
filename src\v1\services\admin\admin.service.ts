import { AdminStatus } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import SendAdminInviteMail from '@common/services/email-notifications/admin/invite.service';
import { PopulatedAdminAccount, ProspectAdmin } from '@common/types/admin.types';
import TYPES from '@common/types/inversify.types';
import { AdminAccountRepository } from '@v1/repositories/admin-account.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import argon2 from 'argon2';
import { inject, injectable } from 'inversify';
import mongoose from 'mongoose';
import 'reflect-metadata';

@injectable()
class AdminService {
  private userRepository;
  private sendAdminInviteMail;
  private adminAccountRepository;

  constructor(
    @inject(TYPES.UserRepository) userRepository: UserRepository,
    @inject(TYPES.AdminAccountRepository) adminAccountRepository: AdminAccountRepository,
    @inject(TYPES.SendAdminInviteMail) sendAdminInviteMail: SendAdminInviteMail,
  ) {
    this.userRepository = userRepository;
    this.sendAdminInviteMail = sendAdminInviteMail;
    this.adminAccountRepository = adminAccountRepository;
  }

  async createProspectAdminUser(resource: ProspectAdmin) {
    try {
      const user = await this.userRepository.getUserByEmail(resource.email.toString());

      if (user) {
        throw new CustomError(400, 'Email is already tied to an account');
      }

      let prospect = await this.userRepository.createProspectAdminUser(resource);

      let admin = await this.adminAccountRepository.create({ user: prospect._id });

      await this.sendAdminInviteMail.send(prospect.email, { userId: prospect._id.toString() });
      await this.adminAccountRepository.updateByUserId({ userId: admin.user });
      await this.userRepository.update({ _id: prospect._id, admin: admin._id });

      return { _id: prospect._id, email: prospect.email };
    } catch (error: any) {
      throw new CustomError(error.statusCode || 500, error.message);
    }
  }

  async getAdmins(resource: any) {
    try {
      const admins: any = await this.adminAccountRepository.getAdmins(
        resource.page,
        resource.limit,
        resource?.startDate,
        resource?.endDate,
        resource?.status,
      );

      const data: object[] = [];

      admins.data.forEach((admin: PopulatedAdminAccount) => {
        data.push({
          _id: admin.user._id,
          firstName: admin.user.firstName,
          lastName: admin.user.lastName,
          phoneNumber: admin.user.phoneNumber,
          email: admin.user.email,
          role: admin.user.role,
          profilePicture: admin.user.profilePicture,
          lastLoggedInAt: admin.user.lastLoggedInAt,
          isVerified: admin.user.isVerified,
          verifiedAt: admin.user.verifiedAt,
          createdAt: admin.createdAt,
          updatedAt: admin.updatedAt,
          admin: {
            status: admin.status,
          },
        });
      });

      admins.data = data;

      return admins;
    } catch (error: any) {
      throw new CustomError(error.statusCode || 500, error.message);
    }
  }

  async getByAdminId(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new CustomError(404, 'Admin not found');
    }

    const user = await this.userRepository.getById(id);

    if (!user) {
      throw new CustomError(404, 'Admin not found');
    }

    const admin = await this.adminAccountRepository.getByUserId(id);

    return admin;
  }

  async updateAdminById(resource: any) {
    const user = await this.userRepository.update(resource);
    const admin = await this.adminAccountRepository.getByUserId(user._id);

    return admin;
  }

  async updateAdminPassword(resource: any) {
    const user = await this.userRepository.getUserById(resource.userId);

    if (!user) {
      throw new CustomError(404, 'User not found');
    }

    if (!(await argon2.verify(String(user?.password), resource.currentPassword))) {
      throw new CustomError(400, 'Password is not correct');
    }                

    await this.userRepository.update({ _id: resource.userId, password: await(argon2.hash(resource.newPassword)) });
  }

  async toggleAdminSuspension(id: string, isSuspension: boolean = false) {
    const user = await this.userRepository.getById(id);

    const admin = await this.adminAccountRepository.getByUserId(id);

    if (admin?.status === AdminStatus.SUSPENDED && isSuspension) {
      throw new CustomError(409, 'Admin already suspended!');
    }

    if (admin?.status === AdminStatus.ACTIVE && !isSuspension) {
      throw new CustomError(409, 'Admin already active!');
    }

    if (admin && admin.status !== AdminStatus.SUSPENDED && isSuspension) {
      const updatedAdmin = await this.adminAccountRepository.updateByUserId({ userId: admin.user, status: AdminStatus.SUSPENDED });

      return updatedAdmin;
    }

    if (admin && admin.status === AdminStatus.SUSPENDED && !isSuspension && user.isVerified === true) {
      const updatedAdmin = await this.adminAccountRepository.updateByUserId({ userId: admin.user, status: AdminStatus.ACTIVE });

      return updatedAdmin;
    }

    if (admin && admin.status === AdminStatus.SUSPENDED && !isSuspension && user.isVerified === false) {
      const updatedAdmin = await this.adminAccountRepository.updateByUserId({ userId: admin.user, status: AdminStatus.NOT_VERIFIED });

      return updatedAdmin;
    }

    return admin;
  }

  async toggleAdminBlockage(id: string, isBlockage: boolean = false) {
    const user = await this.userRepository.getById(id);

    const admin = await this.adminAccountRepository.getByUserId(id);

    if (admin?.status === AdminStatus.BLOCKED && isBlockage) {
      throw new CustomError(400, 'Admin already blocked!');
    }

    if (admin?.status === AdminStatus.ACTIVE && !isBlockage) {
      throw new CustomError(400, 'Admin already active!');
    }

    if (admin && admin.status !== AdminStatus.BLOCKED && isBlockage) {
      const updatedAdmin = await this.adminAccountRepository.updateByUserId({ userId: admin.user, status: AdminStatus.BLOCKED });

      return updatedAdmin;
    }

    if (admin && admin.status === AdminStatus.BLOCKED && !isBlockage && user.isVerified === true) {
      const updatedAdmin = await this.adminAccountRepository.updateByUserId({ userId: admin.user, status: AdminStatus.ACTIVE });

      return updatedAdmin;
    }

    if (admin && admin.status === AdminStatus.BLOCKED && !isBlockage && user.isVerified === false) {
      const updatedAdmin = await this.adminAccountRepository.updateByUserId({ userId: admin.user, status: AdminStatus.NOT_VERIFIED });

      return updatedAdmin;
    }

    return admin;
  }

  async search(resource: any) {
    return await this.userRepository.searchAdmins(resource.page, resource.limit, resource.word);
  }
}

export { AdminService };
