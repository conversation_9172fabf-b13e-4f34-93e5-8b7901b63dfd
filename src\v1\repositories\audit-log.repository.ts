import { AuditLog } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class AuditLogRepository extends BaseRepository {
  constructor() {
    super(AuditLog);
  }

  async findAuditLogs(filter: any, limit: number, page: number) {
    return await AuditLog.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate('user', '-_id -__v -createdAt -updatedAt -phoneNumber -role -isVerified -verifiedAt -lastLoggedInAt -admin -merchant -profilePicture')
      .select('-__v')
      .sort('-createdAt')
      .exec();
  }

  async getAuditLogs(page: number = 1, limit: number = 10, startDate: Date = new Date(1970), endDate: Date = new Date()) {
    const filter = { createdAt: { $gte: startDate, $lte: endDate } };

    const auditLogs = await this.findAuditLogs(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, auditLogs);
  }

  async getAuditLog(_id: string) {
    return await AuditLog.findOne({ _id })
      .populate('user', '-_id -__v -createdAt -updatedAt -phoneNumber -role -isVerified -verifiedAt -lastLoggedInAt -admin -merchant -profilePicture')
      .select('-__v')
      .sort('-createdAt')
      .exec();
  }

  async search(page: number, limit: number, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');

    const filter = { $or: [{ activity: regex }] };

    const data = await this.findAuditLogs(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }
}

export { AuditLogRepository };

export default new AuditLogRepository();
