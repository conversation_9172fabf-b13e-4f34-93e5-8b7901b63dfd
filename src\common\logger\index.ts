import config from '@common/config/env.config';
import { createLogger, format, transports } from 'winston';
import * as Transport from 'winston-transport';

let logTransports: Array<Transport>;

if (config.NODE_ENV !== 'production') {
  logTransports = [new transports.Console()];
} else {
  logTransports = [new transports.File({ filename: 'logs/logs.log' })];
}

let logFormat = format.combine(
  format.colorize(),
  format.splat(),
  format.metadata(),
  format.timestamp(),
  format.printf(({ timestamp, level, message, metadata }) => {
    return `[${timestamp}] ${level}: ${message}. ${JSON.stringify(metadata)}`;
  }),
);

let appLogger = createLogger({ transports: logTransports, format: logFormat });

export default appLogger;
