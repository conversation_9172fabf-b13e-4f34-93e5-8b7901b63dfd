import { UserRole } from '@common/enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { InvoiceSchema } from '@v1/dtos/invoice.dtos';
import { AdminSubscriptionPlanService } from '@v1/services/admin/subscription-plan.service';
import { InvoiceService } from '@v1/services/invoice.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, httpPost, request, response } from 'inversify-express-utils';
import jwt from 'jsonwebtoken';

@controller('/invoice', Auth.validateAuth([UserRole.MERCHANT]))
class InvoiceController {
  constructor(@inject(TYPES.InvoiceService) private invoiceService: InvoiceService) {}

  @httpGet('/subscription')
  public async getMerchantSubscription(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const subscription = await this.invoiceService.getMerchantActiveSubscription(res.locals.jwt._id);
      res.handleSuccessResponse(200, `Merchant's active subscription retrieved successfully`, subscription);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('/subscription', validate(InvoiceSchema.createMerchantSubscription))
  public async createMerchantSubscription(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchantSubscription = await this.invoiceService.createMerchantSubscription({ ...req.body, merchantId: res.locals.jwt._id });
      res.handleSuccessResponse(201, 'Merchant subscription created successfully', merchantSubscription);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
}
