import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class UserSuccessfulPasswordChangeMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string }): Promise<EmailNotifier> {
    this.emailNotifier.setMailProperties('Password successfully changed', 'wadoo.user.password_change_successful', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default UserSuccessfulPasswordChangeMail;
