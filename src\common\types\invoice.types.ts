import { SubscriptionStatus } from '@common/enums/index.enums';
import { InvoiceSchema } from '@v1/dtos/invoice.dtos';
import { z } from 'zod';

export type CreateMerchantSubscription = z.infer<typeof InvoiceSchema.createMerchantSubscription>['body'] & {
  merchantId: string;
  merchant: string;
  startDate: Date;
  endDate: Date;
};

export type MerchantSubscriptionData = {
  _id: string;
  subscriptionPlan: {
    _id: string;
    name: string;
    amount: number;
  };
  merchant: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  startDate: Date;
  endDate: Date;
  status: SubscriptionStatus;
  createdAt: Date;
  updatedAt: Date;
};
