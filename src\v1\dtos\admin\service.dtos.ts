import { z as schema } from 'zod';

const serviceId = schema.object({
  params: schema
    .object({
      service_id: schema.string(),
    })
    .strict(),
});

const createService = schema.object({
  body: schema
    .object({
      merchantId: schema.string(),
      name: schema.string(),
      duration: schema.string(),
      description: schema.string(),
      price: schema.string().or(schema.number()),
      reminderFrequency: schema.string().or(schema.number()),
      isNegotiable: schema.boolean().or(schema.string()),
    })
    .strict(),
});

const updateService = schema.object({
  body: schema
    .object({
      serviceId: schema.string(),
      name: schema.string().optional(),
      duration: schema.string().optional(),
      description: schema.string().optional(),
      price: schema.string().optional(),
      reminderFrequency: schema.number().optional(),
      isNegotiable: schema.boolean().optional(),
    })
    .strict(),
});

const bodyId = schema.object({
  body: schema
    .object({
      serviceId: schema.string(),
    })
    .strict(),
});

export const AdminServiceSchema = {
  bodyId,
  serviceId,
  updateService,
  createService,
};
