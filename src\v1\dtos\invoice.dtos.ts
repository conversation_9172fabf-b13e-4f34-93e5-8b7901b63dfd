import { z as schema } from 'zod';

const createMerchantSubscription = schema.object({
  body: schema
    .object({
      subscriptionPlanId: schema.string(),
      paymentDetails: schema.object({
        cardNumber: schema.string().length(16, { message: 'Card number must contain 16 digits' }),
        expiryDate: schema.string(),
        cvv: schema.string().length(3, { message: 'CVV must contain 3 digits' }),
      }),
    })
    .strict(),
});

export const InvoiceSchema = {
  createMerchantSubscription
}