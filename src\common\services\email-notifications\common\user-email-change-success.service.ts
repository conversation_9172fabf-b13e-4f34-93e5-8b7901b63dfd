import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class EmailChangeSuccessMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string }): Promise<EmailNotifier> {
    this.emailNotifier.setMailProperties('Email change successful', 'wadoo.user.email_change_success_message', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default EmailChangeSuccessMail;
