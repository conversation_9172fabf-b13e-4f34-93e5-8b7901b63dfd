import { BusinessType } from "@common/schema/index.schema";
import { injectable } from "inversify";
import "reflect-metadata";
import BaseRepository from "./base.repository";

@injectable()
class BusinessTypeRepository extends BaseRepository{

  constructor() {
    super(BusinessType);
  }

  async getBusinessTypes(){
    return await BusinessType.find().select('-__v').sort('name').exec();
  }
}

export { BusinessTypeRepository };

export default new BusinessTypeRepository()

