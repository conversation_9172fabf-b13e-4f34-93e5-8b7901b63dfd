import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import express from 'express';

interface JsonResponse {
  status: boolean;
  message: string;
  data: any;
}

const handleResponse: any = (responseCode: number, message: string, data: any, res: CustomResponse, status: boolean) => {
  const jsonResponse: JsonResponse = {
    status, // Status was made variable to cater for batch create/update
    message,
    data,
  };
  res.status(responseCode).json(jsonResponse);
};

const handleError: any = (error: any, res: CustomResponse) => {
  let responseMessage: string | null = null;
  const errorMessages: string[] = [];

  if (error.name === 'ZodError') {
    error.issues.map((issue: any) => {
      let errorMessage: string;
      if (issue.code === 'invalid_type') {
        if (issue.path.length === 2) {
          errorMessage = issue.received === 'undefined' ? `${issue.path[1]} is required` : `${issue.path[1]} type is invalid`;
        } else if (issue.path.length === 3) {
          errorMessage =
            issue.received === 'undefined'
              ? `${issue.path[2]} is missing in ${issue.path[1]}`
              : `${issue.path[2]} type in ${issue.path[1]} is invalid`;
        } else {
          errorMessage =
            issue.received === 'undefined'
              ? `${issue.path[3]} is missing on entry ${+issue.path[2] + 1} of ${issue.path[1]}`
              : `${issue.path[3]} type on entry ${+issue.path[2] + 1} of ${issue.path[1]} is invalid`;
        }
        errorMessages.push(errorMessage);
      }

      if (issue.code === 'invalid_union') {
        const unionIssue = issue.unionErrors[0].issues[0];
        errorMessage = unionIssue.received === 'undefined' ? `${unionIssue.path[1]} is required` : `${unionIssue.path[1]} type is invalid`;
        errorMessages.push(errorMessage);
      }

      if (issue.code === 'too_small' || issue.code === 'too_big') {
        errorMessages.push(issue.message);
      }

      if (issue.code === 'unrecognized_keys') {
        errorMessage = issue.keys.length === 1 ? `${issue.keys[0]} is an invalid key` : `${issue.keys.join(', ')} are invalid keys`;
        errorMessages.push(errorMessage);
      }

      if (issue.code === 'invalid_enum_value') {
        errorMessage =  `${issue.path[1]} should be ${issue.options.join(' or ')}`;
        errorMessages.push(errorMessage);
      }

      if (issue.hasOwnProperty('validation')) {
        errorMessage = issue.message;
        errorMessages.push(errorMessage);
      }
    });

    responseMessage = errorMessages.join('. ') + '.';
    error.statusCode = 400;
  }

  if (error.name === 'CastError') {
    responseMessage = 'No result found';
    error.statusCode = 404;
  }

  const jsonResponse: JsonResponse = {
    status: false,
    message: responseMessage || error.message || 'An error occurred',
    data: null,
  };

  // if (Object.keys(error).length !== 0 && !(error instanceof CustomError)) {
  //   jsonResponse.data = error;
  // }
  res.status(error.status || error.statusCode || 500).json(jsonResponse);
};

export default function responseHandlerMiddleware() {
  return (req: express.Request, res: CustomResponse, next: express.NextFunction) => {
    res.handleSuccessResponse = (responseStatusCode: number, message: string, data: any = null, status: boolean = true) => {
      return handleResponse(responseStatusCode, message, data, res, status);
    };

    res.handleErrorResponse = (error: any) => {
      return handleError(error, res);
    };

    next();
  };
}
