import { uploadProfilePicture, uploadReviewImage } from '@common/config/multer.config';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import express from 'express';
import { injectable } from 'inversify';
import 'reflect-metadata';

@injectable()
class UploadMiddleware {
  uploadProfilePicture = () => {
    return async (req: express.Request, res: CustomResponse, next: express.NextFunction) => {
      uploadProfilePicture(req, res, async err => {
        if (err) {
          return res.status(err.statusCode || 400).json({ status: false, message: err.message || 'Unsupported image file' });
        }

        next();
      });
    };
  };

  uploadReviewImage() {
    return async (req: express.Request, res: CustomResponse, next: express.NextFunction) => {
      uploadReviewImage(req, res, async err => {
        if (err) {
          return res.status(err.statusCode || 400).json({ status: false, message: err.message || 'Unsupported image file' });
        }

        next();
      });
    };
  }
}

export { UploadMiddleware };

export default new UploadMiddleware();
