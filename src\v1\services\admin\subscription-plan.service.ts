import CustomError from '@common/helpers/custom-error.helper';
import TYPES from '@common/types/inversify.types';
import { CreateSubscriptionPlan, ErrorData, SuccessData, UpdateSubscriptionPlan } from '@common/types/subscription-plan.types';
import { SubscriptionPlanRepository } from '@v1/repositories/subscription-plan.repository';
import { inject, injectable } from 'inversify';
import mongoose, { mongo } from 'mongoose';

@injectable()
class AdminSubscriptionPlanService {
  constructor(@inject(TYPES.SubscriptionPlanRepository) private subscriptionPlanRepository: SubscriptionPlanRepository) {}

  async getSubscriptionPlans() {
    return await this.subscriptionPlanRepository.getSubscriptionPlans();
  }

  async createSubscriptionPlan(resource: CreateSubscriptionPlan) {
    const plan = await this.subscriptionPlanRepository.getSubscriptionPlanByName(resource.name);
    if (plan) {
      throw new CustomError(409, 'Subscription plan name already exists');
    }

    const createdPlanData = await this.subscriptionPlanRepository.create(resource);
    const { isDeleted, __v, _id, ...data } = createdPlanData.toObject();
    return { _id, ...data };
  }

  async updateSubscriptionPlans(resource: UpdateSubscriptionPlan) {
    const { subscriptionPlans: plans } = resource;
    const successData: any[] = [];
    const errorData: ErrorData[] = [];

    for (const plan of plans) {
      if (!mongoose.Types.ObjectId.isValid(plan.subscriptionPlanId)) {
        errorData.push({ subscriptionPlanId: plan.subscriptionPlanId, errorMessage: 'Subscription plan not found' });
        continue;
      }

      if (plan.name) {
        const planData = await this.subscriptionPlanRepository.getSubscriptionPlanByName(plan.name);
        // Prevent a plan from being renamed to a name that already belongs to another plan
        if (String(planData?._id) !== plan.subscriptionPlanId && planData?.name === plan.name) {
          errorData.push({ subscriptionPlanId: plan.subscriptionPlanId, errorMessage: 'Subscription plan name already exists' });
          continue;
        }
      }

      const subscriptionPlan = await this.subscriptionPlanRepository.updateSubscriptionPlans(plan);
      if (subscriptionPlan) {
        successData.push(subscriptionPlan);
      } else {
        errorData.push({ subscriptionPlanId: plan.subscriptionPlanId, errorMessage: 'Subscription plan not found' });
      }
    }

    return { successData, errorData };
  }

  async deleteSubscriptionPlan(subscriptionPlanId: string) {
    if (!mongoose.Types.ObjectId.isValid(subscriptionPlanId)) {
      throw new CustomError(404, 'Subscription plan does not exist');
    }
    const plan = await this.subscriptionPlanRepository.delete(subscriptionPlanId);
    if (!plan) {
      throw new CustomError(404, 'Subscription plan does not exist');
    }

    return plan;
  }
}

export { AdminSubscriptionPlanService };
