import AdminAccountRepository from '@v1/repositories/admin-account.repository';
import AuthTokenRepository from '@v1/repositories/auth-token.repository';
import MerchantAccountRepository from '@v1/repositories/merchant-account.repository';
import express from 'express';
import { injectable } from 'inversify';
import jwt, { JsonWebTokenError, Jwt } from 'jsonwebtoken';
import 'reflect-metadata';
import UserRepository from '@v1/repositories/user.repository';
import config from '../config/env.config';
import { AdminStatus, MerchantStatus, UserRole } from '../enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';

@injectable()
class AuthMiddleware {
  validateAuth(roles: UserRole[] | null = null) {
    return async (req: express.Request, res: CustomResponse, next: express.NextFunction) => {
      const authHeader = req.headers.authorization;
      if (!authHeader) {
        return res.status(401).json({ status: false, message: 'No token found' });
      }

      try {
        const authorization = authHeader.split(' ');

        if (authorization[0] !== 'Bearer' || !authorization[1]) {
          return res.status(401).json({ status: false, message: 'No token found' });
        }

        res.locals.jwt = jwt.verify(authorization[1], config.REFRESH_TOKEN_SECRET_KEY || '') as Jwt;

        if (!roles) {
          this.validateUser(req, res, next);
          return;
        }

        if (res.locals.jwt && roles.includes(res.locals.jwt.role)) {
          this.validateUser(req, res, next);
          return;
        }

        return res.status(403).json({ status: false, message: 'Access denied' });
      } catch (error: any) {
        res.status(401).json({ status: false, message: error.message });
      }
    };
  }

  async validateRefreshToken(req: express.Request, res: CustomResponse, next: express.NextFunction) {
    const hash = await AuthTokenRepository.getToken(req.body.refreshToken);
    let user;

    if (hash) {
      user = await UserRepository.getById(hash.user.toString());
    }

    if (hash?.token === req.body.refreshToken) {
      req.body = user;
      next();
    } else {
      res.status(400).json({ status: false, message: 'Invalid refresh token' });
    }
  }

  async validateUser(req: express.Request, res: CustomResponse, next: express.NextFunction) {
    const email = res.locals?.jwt?.email || req.body.email;
    const user = await UserRepository.getUserByEmail(email);

    if (!user?.isVerified) {
      return res.status(403).json({ status: false, message: 'Account not verified. Kindly verify your account to use this application.' });
    }

    if (user?.role === UserRole.MERCHANT) {
      const merchant = await MerchantAccountRepository.getByEmail(email);

      if (merchant?.status === MerchantStatus.SUSPENDED) {
        return res
          .status(403)
          .json({ status: false, message: 'Account suspended. Kindly reach out to the admin to lift <NAME_EMAIL>' });
      }

      if (merchant?.status === MerchantStatus.BLOCKED) {
        return res
          .status(403)
          .json({ status: false, message: 'Account blocked. Kindly reach out to the admin to lift <NAME_EMAIL>' });
      }
    }

    if (user?.role === UserRole.ADMIN || user?.role === UserRole.ANALYST) {
      const admin = await AdminAccountRepository.getByUserId(user._id.toString());

      if (admin?.status === AdminStatus.SUSPENDED) {
        return res
          .status(403)
          .json({ status: false, message: 'Account suspended. Kindly reach out to the admin to lift <NAME_EMAIL>' });
      }

      if (admin?.status === AdminStatus.BLOCKED) {
        return res
          .status(403)
          .json({ status: false, message: 'Account blocked. Kindly reach out to the admin to lift <NAME_EMAIL>' });
      }
    }

    next();
  }
}

export { AuthMiddleware };

export default new AuthMiddleware();
