import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import UploadMiddleware from '@common/middleware/upload.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { AdminSchema } from '@v1/dtos/admin/admin.dtos';
import { _idBody, dashboardSearch, filterUsers } from '@v1/dtos/common.dtos';
import { logAudit } from '@v1/middleware/admin.middleware';
import { AdminService } from '@v1/services/admin/admin.service';
import { UserService } from '@v1/services/user.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, httpPatch, httpPost, request, response } from 'inversify-express-utils';

@controller('')
export class AdminController {
  private adminService;
  private userService;

  constructor(@inject(TYPES.AdminService) adminService: AdminService, @inject(TYPES.UserService) userService: UserService) {
    this.adminService = adminService;
    this.userService = userService;
  }

  @httpPost(
    '/admin/users/invite',
    Auth.validateAuth([UserRole.SUPER_ADMIN]),
    validate(AdminSchema.createProspectAdminUser),
    logAudit('Admin invitation'),
  )
  public async createProspectAdminUser(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const prospect = await this.adminService.createProspectAdminUser(req.body);
      res.handleSuccessResponse(200, 'Email invitation sent', prospect);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/admin/users/registration', Auth.validateAuth([UserRole.ADMIN, UserRole.ANALYST]), validate(AdminSchema.registerAdmin))
  public async registerAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.userService.updateUserById(req.body, true);
      res.handleSuccessResponse(200, 'Registration successful', admin);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/admin/users', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(filterUsers))
  public async getAdmins(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admins = await this.adminService.getAdmins(req.query);
      res.handleSuccessResponse(200, 'Admins retrieved', admins);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/admin/users/:_id', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(AdminSchema._id))
  public async getAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.adminService.getByAdminId(req.params._id);
      res.handleSuccessResponse(200, 'Admin retrieved', admin);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/super-admin/admin/users',
    Auth.validateAuth([UserRole.SUPER_ADMIN]),
    validate(AdminSchema.updateAdminBySuperAdmin),
    logAudit('Admin update'),
  )
  public async updateAdminBySuperAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.userService.updateUserById(req.body);

      res.handleSuccessResponse(200, `Admin updated`, admin);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/admin/users/profile-picture',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    UploadMiddleware.uploadProfilePicture(),
  )
  public async updateProfilePicture(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.userService.updateProfilePicture(res.locals.jwt._id, req.file);

      if (!admin) {
        throw new CustomError(404, 'Admin not found');
      }

      res.handleSuccessResponse(200, `Profile picture updated`, admin);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/super-admin/admin/users/profile-picture',
    Auth.validateAuth([UserRole.SUPER_ADMIN]),
    UploadMiddleware.uploadProfilePicture(),
    validate(_idBody),
    logAudit('Admin update'),
  )
  public async updateProfilePictureBySuperAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.userService.updateProfilePicture(req.body._id, req.file);

      if (!admin) {
        throw new CustomError(404, 'Admin not found');
      }

      res.handleSuccessResponse(200, `Profile picture updated`, admin);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/admin/users', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(AdminSchema.updateAdmin))
  public async updateAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.userService.updateUserById({ _id: res.locals.jwt._id, ...req.body });
      res.handleSuccessResponse(200, `Admin updated`, admin);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/admin/users/password',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    validate(AdminSchema.updateAdminPassword),
  )
  public async updateAdminPassword(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      await this.adminService.updateAdminPassword({ userId: res.locals.jwt._id, ...req.body });
      res.handleSuccessResponse(200, `Admin password updated successfully`);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/admin/users/suspend', Auth.validateAuth([UserRole.SUPER_ADMIN]), validate(_idBody), logAudit('Admin suspension'))
  public async suspendAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.adminService.toggleAdminSuspension(req.body._id, true);

      if (!admin) {
        throw new CustomError(404, 'Admin not found');
      }

      res.handleSuccessResponse(200, 'Admin suspended');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/admin/users/unsuspend', Auth.validateAuth([UserRole.SUPER_ADMIN]), validate(_idBody), logAudit('Admin suspension lift'))
  public async unsuspendAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.adminService.toggleAdminSuspension(req.body._id);

      if (!admin) {
        throw new CustomError(404, 'Admin not found');
      }

      res.handleSuccessResponse(200, 'Admin unsuspended');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/admin/users/block', Auth.validateAuth([UserRole.SUPER_ADMIN]), validate(_idBody), logAudit('Admin blockage'))
  public async blockAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.adminService.toggleAdminBlockage(req.body._id, true);

      if (!merchant) {
        throw new CustomError(404, 'Admin not found');
      }

      res.handleSuccessResponse(200, 'Admin blocked');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/admin/users/unblock', Auth.validateAuth([UserRole.SUPER_ADMIN]), validate(_idBody), logAudit('Admin blockage lift'))
  public async unblockAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admin = await this.adminService.toggleAdminBlockage(req.body._id);

      if (!admin) {
        throw new CustomError(404, 'Admin not found');
      }

      res.handleSuccessResponse(200, 'Admin unblocked');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/admin/users/search', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(dashboardSearch))
  public async search(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const admins = await this.adminService.search(req.query);

      res.handleSuccessResponse(200, `Admins retrieved`, admins);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
