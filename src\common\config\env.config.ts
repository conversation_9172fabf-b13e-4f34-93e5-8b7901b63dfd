import dotenv from 'dotenv';

dotenv.config();

export default {
  SERVER_PORT: process.env.PORT,
  NODE_ENV: process.env.NODE_ENV,
  REFRESH_TOKEN_SECRET_KEY: process.env.REFRESH_TOKEN_SECRET_KEY,
  MONGODB_DATABASE_URL: process.env.MONGODB_DATABASE_URL,
  MAILGUN_KEY: process.env.MAILGUN_KEY,
  MAILGUN_DOMAIN: process.env.MAILGUN_DOMAIN,
  MAILGUN_USERNAME: process.env.MAILGUN_USERNAME,
  CARBON_BASE_URL: process.env.CARBON_BASE_URL,
  CARBON_MERCHANT_ID: process.env.CARBON_MERCHANT_ID,
  CARBON_API_KEY: process.env.CARBON_API_KEY,
  CARBON_CREATE_WALLET_PATH: '/api/v2/wallet/create',
  CARBON_FETCH_WALLET_DETAILS_PATH: '/api/v2/wallet/',
  CA<PERSON><PERSON>_FETCH_WALLET_STATEMENT_PATH: '/api/v2/wallet/{walletId}/latest-statement',
  CARBON_ACTIVATE_WALLET_PATH: '/api/v2/wallet/activate?reason=',
  CARBON_DEACTIVATE_WALLET_PATH: '/api/v2/wallet/deactivate?reason=',
  CARBON_TRANSFER_PATH: '/api/v2/transfers/sendmoney',
  CARBON_FETCH_BANKS: '/api/v2/transfers/banks',
  CLOUDINARY_CLOUD_NAME: process.env.CLOUDINARY_CLOUD_NAME,
  CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET,
  CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY,
  VFD_BASE_URL: process.env.VFD_BASE_URL,
  VFD_TOKEN: process.env.VFD_TOKEN,
};
