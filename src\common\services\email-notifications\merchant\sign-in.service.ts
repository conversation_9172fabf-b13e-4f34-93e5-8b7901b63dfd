import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class MerchantSignInMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string; password: string }): Promise<any> {
    this.emailNotifier.setMailProperties('Wadoo sign in credentials', 'wadoo.merchant.sign_in_credentials', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default MerchantSignInMail;
