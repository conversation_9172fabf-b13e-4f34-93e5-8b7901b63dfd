import { injectable, inject } from 'inversify';
import 'reflect-metadata';
import { Model } from 'mongoose';
import { DatabaseSeeder } from '@common/interfaces/database-seeder.interface';
import logger from '@common/logger';

@injectable()
class MongooseSeeder implements DatabaseSeeder {
  constructor() {}

  async seed(DatabaseModel: Model<any>, data: []): Promise<void> {
    let dataReturnedFromDatabase = await DatabaseModel.find({});
    if (dataReturnedFromDatabase.length == 0) {
      try {
        await DatabaseModel.insertMany(data);
      } catch (error: any) {
        logger.error('Error encountered while seeding DB', error);
      }
    }
  }
}

export default MongooseSeeder;
