import { injectable, inject } from 'inversify';
import 'reflect-metadata';
import TYPES from '@common/types/inversify.types';
import CustomError from '@common/helpers/custom-error.helper';
import { AuditLogRepository } from '@v1/repositories/audit-log.repository';
import { UserRepository } from '@v1/repositories/user.repository';

@injectable()
class AuditLogService {
  private auditLogRepository;
  private userRepository;

  constructor(
    @inject(TYPES.AuditLogRepository) auditLogRepository: AuditLogRepository,
    @inject(TYPES.UserRepository) userRepository: UserRepository,
  ) {
    this.auditLogRepository = auditLogRepository;
    this.userRepository = userRepository;
  }

  async getAuditLogs(resource: any) {
    try {
      return await this.auditLogRepository.getAuditLogs(resource.page, resource.limit, resource?.startDate, resource?.endDate);
    } catch (error: any) {
      throw new CustomError(error.statusCode || 500, error.message);
    }
  }

  async search(resource: any) {
    return await this.auditLogRepository.search(resource.page, resource.limit, resource.word);
  }
}

export { AuditLogService };
