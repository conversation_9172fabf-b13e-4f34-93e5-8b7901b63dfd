import config from '@common/config/env.config';
import { AdminStatus, AuthTokenType, MerchantStatus, OtpValidationType, UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { getModifiedUser } from '@common/helpers/modify.helper';
import { convertNameToSentenceCase } from '@common/helpers/names.helper';
import AdminWelcomeMail from '@common/services/email-notifications/admin/welcome.service';
import UserAccountVerificationMail from '@common/services/email-notifications/common/user-account-verification.service';
import EmailChangeSuccessMail from '@common/services/email-notifications/common/user-email-change-success.service';
import EmailChangeRequestMail from '@common/services/email-notifications/common/user-email-change.service';
import UserPasswordChangeMail from '@common/services/email-notifications/common/user-password-change.service';
import UserPasswordResetMail from '@common/services/email-notifications/common/user-password-reset.service';
import UserSuccessfulPasswordChangeMail from '@common/services/email-notifications/common/user-successful-password-change.service';
import MerchantWelcomeMail from '@common/services/email-notifications/merchant/welcome.service';
import { CreateUser, ExistingUser, ModifiedUser, UserLogin } from '@common/types/common.types';
import TYPES from '@common/types/inversify.types';
import { CreateMerchant, CreateMerchantAccount } from '@common/types/merchant.types';
import { AdminAccountRepository } from '@v1/repositories/admin-account.repository';
import { AuthTokenRepository } from '@v1/repositories/auth-token.repository';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { OtpRepository } from '@v1/repositories/otp.repository';
import argon2 from 'argon2';
import { inject, injectable } from 'inversify';
import jwt from 'jsonwebtoken';
import cron from 'node-cron';
import 'reflect-metadata';
import { UserRepository } from '../repositories/user.repository';

@injectable()
class AuthService {
  //@ts-expect-error
  refreshTokenSecretKey: string = config.REFRESH_TOKEN_SECRET_KEY;

  constructor(
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
    @inject(TYPES.AuthTokenRepository) private authTokenRepository: AuthTokenRepository,
    @inject(TYPES.OtpRepository) private otpRepository: OtpRepository,
    @inject(TYPES.AdminWelcomeMail) private adminWelcomeMail: AdminWelcomeMail,
    @inject(TYPES.MerchantWelcomeMail) private merchantWelcomeMail: MerchantWelcomeMail,
    @inject(TYPES.UserAccountVerificationMail) private userAccountVerificationMail: UserAccountVerificationMail,
    @inject(TYPES.UserPasswordChangeMail) private userPasswordChangeMail: UserPasswordChangeMail,
    @inject(TYPES.UserPasswordResetMail) private userPasswordResetMail: UserPasswordResetMail,
    @inject(TYPES.UserSuccessfulPasswordChangeMail) private userSuccessfulPasswordChangeMail: UserSuccessfulPasswordChangeMail,
    @inject(TYPES.EmailChangeRequestMail) private emailChangeRequestMail: EmailChangeRequestMail,
    @inject(TYPES.EmailChangeSuccessMail) private emailChangeSuccessMail: EmailChangeSuccessMail,
    @inject(TYPES.MerchantAccountRepository) private merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.AdminAccountRepository) private adminAccountRepository: AdminAccountRepository,
  ) {}

  async merchantSignUp(resource: CreateMerchant) {
    if (await this.userRepository.getUserByEmail(resource.email)) {
      throw new CustomError(409, 'Email is already tied to an account');
    }

    const userDetails: CreateUser = {
      firstName: resource.firstName,
      lastName: resource.lastName,
      phoneNumber: resource.phoneNumber,
      email: resource.email,
      password: resource.password,
    };

    const merchantDetails: Omit<CreateMerchantAccount, 'businessTypeId'> & { businessType: string } = {
      firstName: resource.firstName,
      lastName: resource.lastName,
      phoneNumber: resource.phoneNumber,
      email: resource.email,
      address: resource.address,
      state: resource.state,
      businessType: resource.businessTypeId,
      businessName: resource.businessName,
    };

    const { _id: userId } = await this.userRepository.createUser(userDetails, UserRole.MERCHANT);
    merchantDetails.user = userId.toString();
    const createdMerchant = await this.merchantAccountRepository.create(merchantDetails);
    const updatedUser = await this.userRepository.update({ _id: userId, merchant: createdMerchant._id });
    const { otp } = await this.otpRepository.createOtp(updatedUser, OtpValidationType.ACCOUNT_VERIFICATION);
    await this.userAccountVerificationMail.send(resource.email, { name: convertNameToSentenceCase(resource.firstName), code: otp });
  }

  async signIn(resource: UserLogin) {
    const user = await this.userRepository.getUserByEmail(resource.email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    if (user.password && !(await argon2.verify(user.password, resource.password))) {
      throw new CustomError(401, 'Wrong password');
    }

    const modifiedUser: ModifiedUser = await getModifiedUser(user);
    const userTokens = await this.getAuthenticationTokens(modifiedUser);
    await this.userRepository.update({ _id: modifiedUser._id, lastLoggedInAt: new Date() });

    return { tokens: userTokens, user: modifiedUser };
  }

  async signOut() {
    return {
      accessToken: jwt.sign({}, this.refreshTokenSecretKey, { expiresIn: '1' }),
    };
  }

  async getAuthenticationTokens(user: ModifiedUser) {
    try {
      const userRefreshToken = await this.authTokenRepository.getAuthTokenByUserId(user._id);

      if (userRefreshToken && userRefreshToken.expiry.getTime() > Date.now()) {
        // @ts-expect-error
        const accessToken = await this.createAccessToken(user, userRefreshToken.key);
        return { accessToken, refreshToken: userRefreshToken.token };
      } else {
        return await this.createAuthenticationTokens(user);
      }
    } catch (error: any) {
      throw new CustomError(error.statusCode || 500, error.message);
    }
  }

  async createAuthenticationTokens(user: ModifiedUser) {
    const newRefreshParameters = await this.createRefreshToken(user);
    const userRefreshToken = newRefreshParameters.refreshToken;
    const accessToken = await this.createAccessToken(user, newRefreshParameters.refreshKey);
    return { accessToken, refreshToken: userRefreshToken };
  }

  async createAccessToken(user: ModifiedUser, refreshKey: Buffer) {
    user.refreshKey = refreshKey;
    const accessToken = jwt.sign(user, this.refreshTokenSecretKey, { expiresIn: '7d' });
    delete user.refreshKey;
    return accessToken;
  }

  async createRefreshToken(user: ModifiedUser) {
    const authToken = await this.authTokenRepository.createAuthToken(user, AuthTokenType.REFRESH_TOKEN);
    const refreshToken = authToken.token;
    const refreshKey = authToken.key;

    return { refreshToken, refreshKey };
  }

  async refreshAuthenticationToken(user: ExistingUser) {
    const modifiedUser: ModifiedUser = await getModifiedUser(user);
    const userRefreshToken = await this.authTokenRepository.getAuthTokenByUserId(modifiedUser._id);

    if (userRefreshToken) {
      if (userRefreshToken.expiry.getTime() < Date.now()) {
        throw new CustomError(403, 'Refresh token expired, log in again');
      } else {
        //@ts-expect-error
        const accessToken = await this.createAccessToken(modifiedUser, userRefreshToken.key);
        const userTokens = { accessToken, refreshToken: userRefreshToken.token };
        return { tokens: userTokens, user: modifiedUser };
      }
    }
  }

  async sendAccountVerificationOtp(email: string) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    if (user.firstName) {
      const code = await this.sendOtp(email, OtpValidationType.ACCOUNT_VERIFICATION);
      await this.userAccountVerificationMail.send(email, { name: convertNameToSentenceCase(user.firstName), code });
    }
  }

  async verifyOtp(otp: string, userId: string, otpValidationType: OtpValidationType) {
    const userOtp = await this.otpRepository.getOtp(otp);

    if (!userOtp) {
      throw new CustomError(401, 'Invalid code');
    }
    if (userOtp.expiry.getTime() < Date.now()) {
      throw new CustomError(403, 'Code expired');
    }

    if (userOtp.validationType !== otpValidationType || userOtp.user.toString() !== userId) {
      throw new CustomError(401, 'Invalid code');
    }

    if (otpValidationType === OtpValidationType.ACCOUNT_VERIFICATION) {
      await this.userRepository.update({ _id: userId, isVerified: true, verifiedAt: new Date() });
    }
  }

  async verifyAccount(resource: any) {
    const user = await this.userRepository.getUserByEmail(resource.email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    await this.verifyOtp(resource.code, user._id.toString(), OtpValidationType.ACCOUNT_VERIFICATION);
    await this.otpRepository.deleteOtp(resource.code);

    //send welcome mail and update admin/merchant account
    if (user.role === UserRole.ADMIN || user.role === UserRole.ANALYST) {
      await this.adminWelcomeMail.send(user.email, { name: user.firstName ? convertNameToSentenceCase(user.firstName) : '' });
      return await this.adminAccountRepository.updateByUserId({ userId: user._id, status: AdminStatus.ACTIVE });
    }

    if (user.role === UserRole.MERCHANT && user.firstName) {
      await this.merchantWelcomeMail.send(user.email, { name: convertNameToSentenceCase(user.firstName) });
      return await this.merchantAccountRepository.updateByUserId({ userId: user._id.toString(), status: MerchantStatus.ACTIVE });
    }

    const verifiedUser = await this.userRepository.getById(user._id.toString());
    return getModifiedUser(verifiedUser);
  }

  async sendPasswordResetOtp(email: string) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    const code = await this.sendOtp(email, OtpValidationType.PASSWORD_RESET);
    if (user.firstName) await this.userPasswordResetMail.send(email, { name: convertNameToSentenceCase(user.firstName), code });
  }

  async resetPassword(resource: any) {
    const user = await this.userRepository.getUserByEmail(resource.email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    await this.verifyOtp(resource.code, user._id.toString(), OtpValidationType.PASSWORD_RESET);

    const password = await argon2.hash(resource.newPassword);
    await this.userRepository.update({ _id: user._id.toString(), password });

    if (user.firstName) await this.userSuccessfulPasswordChangeMail.send(user.email, { name: convertNameToSentenceCase(user.firstName) });

    await this.otpRepository.deleteOtp(resource.code);
  }

  async sendPasswordChangeOtp(email: string) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    const code = await this.sendOtp(email, OtpValidationType.PASSWORD_CHANGE);
    if (user.firstName) await this.userPasswordChangeMail.send(email, { name: convertNameToSentenceCase(user.firstName), code });
  }

  async sendOtp(email: string, otpType: OtpValidationType) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    try {
      const { otp } = await this.otpRepository.createOtp(user, otpType);
      return otp;
    } catch (error: any) {
      throw new CustomError(500, 'Problem creating OTP');
    }
  }

  async changePassword(email: string, resource: any) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    if (user.password && !(await argon2.verify(user.password, resource.currentPassword))) {
      throw new CustomError(401, 'Current password is incorrect');
    }

    await this.verifyOtp(resource.code, user._id.toString(), OtpValidationType.PASSWORD_CHANGE);

    const password = await argon2.hash(resource.newPassword);
    await this.userRepository.update({ _id: user._id.toString(), password });

    if (user.firstName) await this.userSuccessfulPasswordChangeMail.send(user.email, { name: convertNameToSentenceCase(user.firstName) });

    await this.otpRepository.deleteOtp(resource.code);
  }

  async sendEmailChangeRequestOtp(email: string) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(404, 'Email not recognized');
    }

    const code = await this.sendOtp(email, OtpValidationType.EMAIL_CHANGE);

    if (user.firstName) await this.emailChangeRequestMail.send(email, { name: convertNameToSentenceCase(user.firstName), code });
  }

  async changeEmail(email: string, resource: any) {
    const user = await this.userRepository.getUserByEmail(email);

    if (!user) {
      throw new CustomError(401, 'Current email is incorrect');
    }

    const userFromNewMail = await this.userRepository.getUserByEmail(resource.newEmail);

    if (userFromNewMail) {
      throw new CustomError(400, 'Email is already tied to an account');
    }

    await this.verifyOtp(resource.code, user._id.toString(), OtpValidationType.EMAIL_CHANGE);

    const updatedUser = await this.userRepository.update({ _id: user._id.toString(), email: resource.newEmail });

    if (user.role === UserRole.MERCHANT)
      await this.merchantAccountRepository.updateByUserId({ userId: user._id.toString(), email: resource.newEmail });

    if (user.firstName) await this.emailChangeSuccessMail.send(resource.newEmail, { name: convertNameToSentenceCase(user.firstName) });

    await this.otpRepository.deleteOtp(resource.code);

    return updatedUser;
  }

  async deleteExpiredEntries() {
    cron.schedule(
      '0 1 * * *',
      async () => {
        await this.authTokenRepository.deleteExpiredAuthTokens();
        await this.otpRepository.deleteExpiredOtps();
      },
      {
        scheduled: true,
        timezone: 'Africa/Lagos',
      },
    );
  }
}

export { AuthService };
