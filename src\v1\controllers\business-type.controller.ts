import { UserRole } from "@common/enums/index.enums";
import { CustomResponse } from "@common/interfaces/custom-response.interface";
import Auth from "@common/middleware/auth.middleware";
import TYPES from "@common/types/inversify.types";
import { BusinessTypeService } from "@v1/services/business-type.service";
import express from "express";
import { inject } from "inversify";
import {
  controller, httpGet,
  request, response
} from 'inversify-express-utils';

@controller('/business-types', Auth.validateAuth([UserRole.MERCHANT]))
class BusinessTypeController {
  private businessTypeService;

  constructor(@inject(TYPES.BusinessTypeService) businessTypeService: BusinessTypeService) {
    this.businessTypeService = businessTypeService;
  }

  @httpGet('')
  public async getBusinessTypes(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.businessTypeService.getBusinessTypes();
      res.handleSuccessResponse(200, 'List of business types retrieved', data);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}