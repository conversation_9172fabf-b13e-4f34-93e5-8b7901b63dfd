import CustomError from '@common/helpers/custom-error.helper';
import TYPES from '@common/types/inversify.types';
import { VirtualAccount } from '@v1/interfaces/virtual-account.interface';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { inject, injectable } from 'inversify';

enum accountOwnerGender {
  MALE = 'Male',
  FEMALE = 'Female',
  male = 'male',
  female = 'female',
}

@injectable()
export class WalletService {
  private merchantAccountRepository;
  private virtualAccount;

  constructor(
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.VirtualAccount) virtualAccount: VirtualAccount,
  ) {
    this.merchantAccountRepository = merchantAccountRepository;
    this.virtualAccount = virtualAccount;
  }

  async createWallet(resource: any) {
    const merchant = await this.merchantAccountRepository.getByUserId(resource.merchantId);

    if (!merchant) {
      throw new CustomError(404, 'Merchant not found');
    }

    if (resource.genderOnBvn === accountOwnerGender.male) {
      resource.genderOnBvn = accountOwnerGender.MALE;
    }

    if (resource.genderOnBvn === accountOwnerGender.female) {
      resource.genderOnBvn = accountOwnerGender.FEMALE;
    }

    let {
      firstNameOnBvn,
      middlenameOnBvn,
      lastNameOnBvn,
      dateOfBirthOnBvn,
      phoneNumberOnBvn,
      emailOnBvn,
      genderOnBvn,
      addressOnBvn,
      stateOnBvn,
      countryOnBvn,
      bvn,
    } = resource;

    let accountOwner = {
      firstNameOnBvn,
      middlenameOnBvn,
      lastNameOnBvn,
      dateOfBirthOnBvn,
      phoneNumberOnBvn,
      emailOnBvn,
      genderOnBvn,
      addressOnBvn,
      stateOnBvn,
      countryOnBvn,
      bvn,
    };

    let virtualAccount = await this.virtualAccount.createAccount(accountOwner);

    if (virtualAccount && virtualAccount.error) {
      throw new CustomError(400, virtualAccount.data.message);
    }

    await this.merchantAccountRepository.updateByUserId({
      userId: resource.merchantId,
      walletId: virtualAccount?.data.body.accountId,
      walletClientId: virtualAccount?.data.body.accountOwner.clientId,
    });

    return virtualAccount;
  }

  async fetchWallet(walletId: string) {
    const wallet = await this.virtualAccount.fetchWalletDetails(walletId);

    return wallet;
  }

  async fetchWalletTransactions(walletId: string, resource: any) {
    const { startDate, endDate, limit } = resource;

    return await this.virtualAccount.fetchWalletStatement(walletId, startDate, endDate, limit);
  }

  async transferFundsFromMerchantWallet(walletId: string, resource: any) {
    const { amount, beneficiaryBankSortCode, beneficiaryAccountNumber, narration } = resource;

    let transferPayload = {
      amount,
      walletId,
      beneficiaryAccountNumber,
      beneficiaryBankSortCode,
      narration,
    };

    return await this.virtualAccount.transferFunds(transferPayload);
  }

  async fetchListOfBanks() {
    return await this.virtualAccount.fetchListOfBanks();
  }
}
