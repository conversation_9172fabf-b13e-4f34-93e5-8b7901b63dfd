import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import UploadMiddleware from '@common/middleware/upload.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { AdminMerchantSchema } from '@v1/dtos/admin/admin-merchant.dtos';
import { dashboardSearch, filterUsers } from '@v1/dtos/common.dtos';
import { MerchantSchema } from '@v1/dtos/merchant.dtos';
import { logAudit } from '@v1/middleware/admin.middleware';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { AdminMerchantService } from '@v1/services/admin/merchant.service';
import { MerchantAccountService } from '@v1/services/merchant-account.service';
import { UserService } from '@v1/services/user.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, httpPatch, httpPost, request, response } from 'inversify-express-utils';

@controller('/admin')
export class AdminMerchantController {
  private userService;
  private merchantAccountService;
  private adminMerchantService;
  private merchantAccountRepository;

  constructor(
    @inject(TYPES.UserService) userService: UserService,
    @inject(TYPES.MerchantAccountService) merchantAccountService: MerchantAccountService,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.AdminMerchantService) adminMerchantService: AdminMerchantService,
  ) {
    this.userService = userService;
    this.merchantAccountService = merchantAccountService;
    this.merchantAccountRepository = merchantAccountRepository;
    this.adminMerchantService = adminMerchantService;
  }

  @httpPost(
    '/merchant',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    UploadMiddleware.uploadProfilePicture(),
    validate(AdminMerchantSchema.createMerchant),
    logAudit('Merchant creation'),
  )
  public async createMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.createMerchant(req.body, UserRole.MERCHANT, UserRole.ADMIN, req.file);
      res.handleSuccessResponse(201, 'Registration successful', merchant);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/merchant/:merchant_id', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(MerchantSchema.merchantId))
  public async getMerchantById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountRepository.getByUserId(req.params.merchant_id);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Merchant retrieved`, merchant);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/merchant/profile-picture',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    UploadMiddleware.uploadProfilePicture(),
    validate(MerchantSchema.bodyId),
  )
  public async updateProfilePictureBySuperAdmin(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.userService.updateProfilePicture(req.body.merchantId, req.file);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Profile picture updated`, merchant);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost(
    '/merchant/verify',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(MerchantSchema.bodyId),
    logAudit('Merchant verification'),
  )
  public async verifyMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.adminMerchantService.verifyMerchant(req.body.merchantId);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Merchant verified`, merchant);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/merchant/suspend',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(MerchantSchema.bodyId),
    logAudit('Merchant suspension'),
  )
  public async suspendMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.adminMerchantService.toggleMerchantSuspension(req.body.merchantId, true);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, 'Merchant suspended');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/merchant/unsuspend',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(MerchantSchema.bodyId),
    logAudit('Merchant suspension lift'),
  )
  public async unsuspendMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.adminMerchantService.toggleMerchantSuspension(req.body.merchantId);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, 'Merchant unsuspended');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost(
    '/merchant/block',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(MerchantSchema.bodyId),
    logAudit('Merchant blockage'),
  )
  public async blockMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.adminMerchantService.toggleMerchantBlockage(req.body.merchantId, true);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, 'Merchant blocked');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost(
    '/merchant/unblock',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(MerchantSchema.bodyId),
    logAudit('Merchant blockage lift'),
  )
  public async unblockMerchant(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.adminMerchantService.toggleMerchantBlockage(req.body.merchantId);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, 'Merchant unblocked');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/merchants', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(filterUsers))
  public async getMerchants(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchants = await this.adminMerchantService.getMerchants(req.query);

      res.handleSuccessResponse(200, 'Merchants retrieved', merchants);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/merchant',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminMerchantSchema.updateMerchant),
    logAudit('Merchant update'),
  )
  public async updateMerchantById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.updateMerchantById(req.body);

      res.handleSuccessResponse(200, `Merchant updated`, merchant);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/merchant/social-media-handles',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(MerchantSchema.socialMediaHandles),
    logAudit('Merchant update'),
  )
  public async setSocialMediaHandles(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchant = await this.merchantAccountService.setSecondaryAccountDetails(req.body);

      if (!merchant) {
        throw new CustomError(404, 'Merchant not found');
      }

      res.handleSuccessResponse(200, `Social media handles of merchant set`, merchant);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/merchants/search', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]), validate(dashboardSearch))
  public async search(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchants = await this.adminMerchantService.search(req.query);

      res.handleSuccessResponse(200, `Merchants retrieved`, merchants);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
