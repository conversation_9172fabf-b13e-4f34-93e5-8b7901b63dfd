import { Service } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class ServiceRepository extends BaseRepository {
  constructor() {
    super(Service);
  }

  async getMerchantServices(merchantId: string) {
    return await Service.find({ user: merchantId }).select('-__v -user -merchant').sort('-createdAt').exec();
  }

  async getMerchantService(resource: { merchantId: string; serviceId: string }) {
    return await Service.findOne({ _id: resource.serviceId, user: resource.merchantId, isDeleted: false }).select('-__v -user -merchant').exec();
  }

  async findServices(filter: any, limit: number, page: number) {
    return await Service.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -walletId -walletClientId -walletAccountNumber -email -user -subscriptionPlan -firstName -lastName -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .select('-__v -user')
      .sort('-createdAt')
      .exec();
  }

  async getServicesByMerchantId(
    merchantId: string,
    page: number = 1,
    limit: number = 10,
    startDate: Date = new Date(1970),
    endDate: Date = new Date(),
  ) {
    const filter = {
      createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isDeleted: false,
      user: merchantId,
    };

    const services = await this.findServices(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, services);
  }

  async getServices(page: number = 1, limit: number = 10, startDate: Date = new Date(1970), endDate: Date = new Date()) {
    const filter = {
      createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isDeleted: false,
    };

    const services = await this.findServices(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, services);
  }

  async getService(_id: string) {
    return await Service.findOne({ _id, isDeleted: false })
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -email -user -subscriptionPlan -walletId -walletClientId -walletAccountNumber -firstName -lastName -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .select('-__v -user')
      .sort('-createdAt')
      .exec();
  }

  async updateServiceById(resource: any) {
    const { _id, ...data } = resource;
    return await Service.findOneAndUpdate({ _id, isDeleted: false }, { $set: data }, { new: true })
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -firstName -walletId -walletClientId -walletAccountNumber -email -user -subscriptionPlan -lastName -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .select('-__v -user')
      .exec();
  }

  async search(page: number = 1, limit: number = 10, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');

    const filter = { $and: [{ $or: [{ name: regex }] }, { isDeleted: false }] };

    const data = await this.findServices(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }

  async searchByMerchantId(merchantId: string, page: number = 1, limit: number = 10, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');

    const filter = { $and: [{ $or: [{ name: regex }] }, { user: merchantId, isDeleted: false }] };

    const data = await this.findServices(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }
}

export { ServiceRepository };
