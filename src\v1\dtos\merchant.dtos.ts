import { z as schema } from 'zod';

const createMerchant = schema.object({
  body: schema
    .object({
      firstName: schema.string(),
      lastName: schema.string(),
      businessTypeId: schema.string(),
      businessName: schema.string(),
      email: schema.string().email(),
      phoneNumber: schema.string(),
      password: schema.string(),
      address: schema.string(),
      state: schema.string(),
    })
    .strict(),
});

const updateMerchant = schema.object({
  body: schema
    .object({
      merchantId: schema.string(),
      firstName: schema.string().optional(),
      lastName: schema.string().optional(),
      businessTypeId: schema.string().optional(),
      businessName: schema.string().optional(),
      phoneNumber: schema.string().optional(),
      address: schema.string().optional(),
      state: schema.string().optional(),
    })
    .strict(),
});

const merchantId = schema.object({
  params: schema
    .object({
      merchant_id: schema.string(),
    })
    .strict(),
});

const sort = schema.object({
  query: schema
    .object({
      startDate: schema.string().optional(),
      endDate: schema.string().optional(),
    })
    .strict(),
});

const setOpeningTimesAndDays = schema.object({
  body: schema
    .object({
      merchantId: schema.string(),
      openingTime: schema.string().optional(),
      closingTime: schema.string().optional(),
      openingDays: schema.string().optional(),
    })
    .strict(),
});

const socialMediaHandles = schema.object({
  body: schema
    .object({
      merchantId: schema.string(),
      twitterHandle: schema.string().optional(),
      instagramHandle: schema.string().optional(),
      facebookUsername: schema.string().optional(),
    })
    .strict(),
});

const bodyId = schema.object({
  body: schema
    .object({
      merchantId: schema.string(),
    })
    .strict(),
});

export const MerchantSchema = {
  sort,
  bodyId,
  merchantId,
  createMerchant,
  updateMerchant,
  socialMediaHandles,
  setOpeningTimesAndDays,
};
