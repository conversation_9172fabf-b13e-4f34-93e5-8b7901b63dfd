export type CreateMerchantAccount = {
    user?: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
    address: string;
    state: string;
    businessTypeId: string;
    businessName: string;
    openingTime?: string;
    closingTime?: string;
    openingDays?: string;
}

export type CreateMerchant = {
    firstName: string;
    lastName: string;
    password: string;
    businessTypeId: string;
    businessName: string;
    email: string;
    phoneNumber: string;
    address: string;
    state: string;
}

export type UpdateMerchant = {
    merchantId: string;
    firstName?: string;
    lastName?: string;
    businessTypeId?: string;
    businessName?: string;
    phoneNumber?: string;
    address?: string;
    state?: string;
}

