import { UserRole } from '@common/enums/index.enums';
import Auth from '@common/middleware/auth.middleware';
import TYPES from '@common/types/inversify.types';
import { logAudit } from '@v1/middleware/admin.middleware';
import { BusinessTypeRepository } from '@v1/repositories/business-type.repository';
import { BusinessTypeService } from '@v1/services/business-type.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, httpPost, request, response } from 'inversify-express-utils';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import { validate } from '@common/middleware/validate.middleware';
import { BusinessTypeSchema } from '@v1/dtos/admin/business-type.dtos';
import { AdminBusinessTypeService } from '@v1/services/admin/business-type.service';

@controller('/admin/business-types')
class AdminBusinessTypeController {
  constructor(@inject(TYPES.AdminBusinessTypeService) private adminBusinessTypeService: AdminBusinessTypeService) {}

  @httpGet('/', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]))
  public async getBusinessTypes(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.adminBusinessTypeService.getBusinessTypes();
      res.handleSuccessResponse(200, 'List of business types retrieved', data);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
  @httpPost('/', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]), validate(BusinessTypeSchema.add), logAudit('Business type(s) addition'))
  public async addBusinessTypes(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.adminBusinessTypeService.addBusinessTypes(req.body);
      res.handleSuccessResponse(200, 'Business type(s) added', data);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
