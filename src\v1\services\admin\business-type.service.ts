import { BusinessTypes } from '@common/types/admin.types';
import TYPES from '@common/types/inversify.types';
import { BusinessTypeRepository } from '@v1/repositories/business-type.repository';
import { injectable, inject } from 'inversify';
import 'reflect-metadata';

@injectable()
class AdminBusinessTypeService {
  constructor(@inject(TYPES.BusinessTypeRepository) private businessTypeRepository: BusinessTypeRepository) {
  }

  async getBusinessTypes() {
    return await this.businessTypeRepository.getBusinessTypes();
  }

  async addBusinessTypes(resource: BusinessTypes) {
    const businessTypes: any = [];

    resource.businessTypes.forEach(businessType => {
      businessTypes.push({ name: businessType });
    });

    const data = await this.businessTypeRepository.create(businessTypes);
    return data.map((item: any) => ({ _id: item._id, name: item.name, createdAt: item.createdAt, updatedAt: item.updatedAt }));
  }
}

export { AdminBusinessTypeService };
