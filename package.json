{"engines": {"node": "16.x"}, "dependencies": {"argon2": "^0.28.5", "axios": "^0.27.2", "change-case": "^5.4.3", "cloudinary": "^1.32.0", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dayjs": "^1.11.10", "debug": "^4.3.4", "dotenv": "^16.0.1", "express": "^4.18.1", "express-winston": "^4.2.0", "form-data": "^4.0.0", "helmet": "^5.1.1", "inversify": "^6.0.1", "inversify-express-utils": "^6.4.3", "jsonwebtoken": "^8.5.1", "mailgun.js": "^10.2.1", "module-alias": "^2.2.2", "mongoose": "^6.4.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "reflect-metadata": "^0.1.13", "winston": "^3.7.2", "zod": "^3.17.3"}, "devDependencies": {"@types/cors": "^2.8.12", "@types/cron": "^2.0.0", "@types/crypto-js": "^4.1.1", "@types/debug": "^4.1.7", "@types/express": "^4.17.13", "@types/jsonwebtoken": "^8.5.8", "@types/multer": "^1.4.7", "@types/node-cron": "^3.0.1", "eslint": "^8.16.0", "nodemon": "^2.0.16", "source-map-support": "^0.5.21", "ts-node": "^10.7.0", "tslint": "^6.1.3", "typescript": "^4.9.5"}, "scripts": {"start": "node ./dist/app.js", "build": "tsc -p tsconfig.json", "debug": "SET DEBUG=* && npm run start", "test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --exec \"npm run build && npm run start\" --watch src --ext ts"}, "_moduleAliases": {"@common": "dist/common", "@config": "dist/config", "@v1": "dist/v1"}}