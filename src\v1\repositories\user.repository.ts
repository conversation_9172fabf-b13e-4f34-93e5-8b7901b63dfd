import { UserRole } from '@common/enums/index.enums';
import { User } from '@common/schema/index.schema';
import { ProspectAdmin } from '@common/types/admin.types';
import argon2 from 'argon2';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class UserRepository extends BaseRepository {
  constructor() {
    super(User);
  }

  async createUser(user: any, role: any) {
    user.password = await argon2.hash(user.password);
    user.role = role;
    return await this.create(user);
  }

  async createProspectAdminUser(resource: ProspectAdmin) {
    return await User.create(resource);
  }

  async updateUserByEmail(email: string, resource: any) {
    return await User.findOneAndUpdate({ email }, { $set: resource }, { new: true }).where('isDeleted').equals(false).exec();
  }

  async getUserByEmail(email: string) {
    return await User.findOne({ email }).select('+password').where('isDeleted').equals(false).exec();
  }

  async getUserById(id: string) {
    return await User.findOne({ _id: id, isDeleted: false }).select('+password').exec();
  }

  async updateByMerchantId(resource: any) {
    const { merchantId, ...data } = resource;
    return await User.findOneAndUpdate({ merchant: merchantId }, { $set: data }, { new: true }).exec();
  }

  async updateByAdminId(resource: any) {
    const { adminId, ...data } = resource;
    return await User.findOneAndUpdate({ admin: adminId }, { $set: data }, { new: true }).exec();
  }

  async getByMerchantId(merchantId: string) {
    return await User.findOne({ merchant: merchantId }).exec();
  }

  async getByAdminId(adminId: string) {
    return await User.findOne({ admin: adminId }).exec();
  }

  async searchAdmins(page: number = 1, limit: number = 10, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');
    const filter = { $and: [{ $or: [{ firstName: regex }, { lastName: regex }, { email: regex }] }, { role: UserRole.ADMIN }] };

    const data = await User.find(filter)
      .select('-__v -role -merchant')
      .populate('admin', '-__v -_id -createdAt -updatedAt -user')
      .limit(limit)
      .skip(limit * (page - 1))
      .sort('-createdAt')
      .exec();

    return await this.getPaginationDetails(filter, limit, page, data);
  }
}

export { UserRepository };

export default new UserRepository();
