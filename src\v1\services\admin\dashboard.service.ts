import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { PotentialCustomerRepository } from '@v1/repositories/potential-customer.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { inject, injectable } from 'inversify';
import TYPES from '@common/types/inversify.types';

@injectable()
class DashboardService {
  private serviceRepository;
  private merchantAccountRepository;
  private potentialCustomerRepository;

  constructor(
    @inject(TYPES.ServiceRepository) serviceRepository: ServiceRepository,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.PotentialCustomerRepository) potentialCustomerRepository: PotentialCustomerRepository,
  ) {
    this.serviceRepository = serviceRepository;
    this.merchantAccountRepository = merchantAccountRepository;
    this.potentialCustomerRepository = potentialCustomerRepository;
  }

  async getDashboardInfo() {
    const totalServices = await this.serviceRepository.getTotalDocuments();
    const totalMerchants = await this.merchantAccountRepository.getTotalDocuments();
    const totalPotentialCustomers = await this.potentialCustomerRepository.getTotalDocuments();

    return { totalMerchants, totalServices, totalPotentialCustomers };
  }
}

export { DashboardService };
