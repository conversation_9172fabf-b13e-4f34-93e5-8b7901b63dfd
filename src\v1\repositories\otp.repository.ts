import { OtpValidationType } from '@common/enums/index.enums';
import { Otp } from '@common/schema/index.schema';
import crypto from 'crypto';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class OtpRepository extends BaseRepository {
  constructor() {
    super(Otp);
  }

  async createOtp(user: any, otpValidationType: OtpValidationType) {
    const otpToken = crypto.randomBytes(3).readUIntBE(0, 3).toString().substring(0, 6);
    const otpExpiryDate = new Date();
    otpExpiryDate.setMinutes(otpExpiryDate.getMinutes() + 15);

    const resource = {
      user: user._id,
      validationType: otpValidationType,
      otp: otpToken,
      expiry: otpExpiryDate,
    };

    return await this.create(resource);
  }

  async getOtp(otp: string) {
    return await Otp.findOne({ otp }).exec();
  }

  async deleteExpiredOtps() {
    await Otp.deleteMany({ expiry: { $lte: new Date() } }).exec();
  }

  async deleteOtp(otp: string) {
    await Otp.deleteOne({ otp }).exec();
  }
}

export { OtpRepository };

