import { RequestMethods } from '@common/enums/index.enums';
import { requestMethod } from '@common/types/common.types';
import axios from 'axios';

export const makeApiCall = async (requestMethod: requestMethod, url: string, headers: any, payload?: any, params?: any) => {
  if (requestMethod === RequestMethods.GET) {
    try {
      const response = await axios.get(url, { headers, params });

      return { error: false, data: response.data };
    } catch (e: any) {
      return { error: true, data: e.response.data || e.request || e.message };
    }
  }
  
  if (requestMethod === RequestMethods.POST) {
    try {
      const response = await axios.post(url, payload, { headers, params });
      return { error: false, data: response.data };
    } catch (e: any) {
      return { error: true, data: e.response.data || e.request || e.message };
    }
  }
};
