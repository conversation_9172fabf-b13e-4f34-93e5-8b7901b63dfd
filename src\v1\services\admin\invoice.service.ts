import { SubscriptionStatus } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { getModifiedSubscriptionData } from '@common/helpers/modify.helper';
import TYPES from '@common/types/inversify.types';
import { MerchantSubscriptionData } from '@common/types/invoice.types';
import { InvoiceRepository } from '@v1/repositories/invoice.repository';
import { inject, injectable } from 'inversify';
import mongoose from 'mongoose';
import { InvoiceService } from '@v1/services/invoice.service';
import { UserRepository } from '@v1/repositories/user.repository';

@injectable()
class AdminInvoiceService {
  constructor(
    @inject(TYPES.InvoiceRepository) private invoiceRepository: InvoiceRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
    @inject(TYPES.InvoiceService) private invoiceService: InvoiceService,
  ) {}

  async getSubscriptions(resource: any) {
    const subscriptionsData: MerchantSubscriptionData[] = [];
    const merchantSubscriptions = await this.invoiceRepository.getSubscriptions(resource.page, resource.limit, resource.startDate, resource.endDate);
    merchantSubscriptions.data.forEach((subscription: MerchantSubscriptionData) => {
      subscriptionsData.push(getModifiedSubscriptionData(subscription));
    });
    merchantSubscriptions.data = subscriptionsData;
    return merchantSubscriptions;
  }

  async getSubscriptionsByMerchantId(resource: any) {
    if (!mongoose.Types.ObjectId.isValid(resource.merchantId) || !(await this.userRepository.getById(resource.merchantId))) {
      throw new CustomError(404, 'Merchant not found');
    }
    const subscriptionsData: MerchantSubscriptionData[] = [];
    const merchantSubscriptions = await this.invoiceRepository.getSubscriptionsByMerchantId(
      resource.merchantId,
      resource.page,
      resource.limit,
      resource.startDate,
      resource.endDate,
    );
    merchantSubscriptions.data.forEach((subscription: MerchantSubscriptionData) => {
      subscriptionsData.push(getModifiedSubscriptionData(subscription));
    });
    merchantSubscriptions.data = subscriptionsData;
    return merchantSubscriptions;
  }

  async getSubscriptionById(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new CustomError(404, 'Subscription not found');
    }
    const subscription = await this.invoiceRepository.getSubscriptionById(id);
    if (!subscription) {
      throw new CustomError(404, 'Subscription not found');
    }
    return getModifiedSubscriptionData(subscription);
  }

  async deleteMerchantSubscription(id: string) {
    if (!mongoose.Types.ObjectId.isValid(id) || !(await this.invoiceRepository.updateDeleteStatus(id))) {
      throw new CustomError(404, 'Subscription not found');
    }
  }

  async toggleMerchantSubscriptionSuspension(id: string, isSuspension: boolean = false) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new CustomError(404, 'Subscription not found');
    }

    const subscription = await this.invoiceRepository.getSubscriptionById(id);

    if (!subscription) {
      throw new CustomError(404, 'Subscription not found');
    }

    if (isSuspension && subscription.status === SubscriptionStatus.SUSPENDED) {
      throw new CustomError(409, 'Subscription is already suspended!');
    }

    if (!isSuspension && subscription.status === SubscriptionStatus.ACTIVE) {
      throw new CustomError(409, 'Subscription is already active!');
    }

    const updatedSubscription = await this.invoiceRepository.updateSubscriptionById({
      id,
      status: isSuspension ? SubscriptionStatus.SUSPENDED : SubscriptionStatus.ACTIVE,
    });

    return updatedSubscription;
  }
}

export { AdminInvoiceService };
