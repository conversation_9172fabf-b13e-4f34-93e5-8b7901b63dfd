import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import TYPES from '@common/types/inversify.types';
import { inject, injectable } from 'inversify';

@injectable()
class UserAccountVerificationMail {
  private emailNotifier;

  constructor(@inject(TYPES.EmailNotifier) emailNotifier: EmailNotifier) {
    this.emailNotifier = emailNotifier;
  }

  async send(recipient: any, data: { name: string; code: string }): Promise<EmailNotifier> {
    this.emailNotifier.setMailProperties('Account verification', 'wadoo.user.account_verification', data);

    return await this.emailNotifier.send(recipient);
  }
}

export default UserAccountVerificationMail;
