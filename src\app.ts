import 'reflect-metadata';
import { InversifyExpressServer } from 'inversify-express-utils';
import express from 'express';
import helmet from 'helmet';
import 'module-alias/register';
import cors from 'cors';
import logger from '@common/logger';
import env from '@common/config/env.config';
import container, { merchantReminder, merchantSubscriptionsCleanup, pictureUploadCleanUp } from '@common/config/inversify.config';
import responseHandlerMiddleware from '@common/middleware/response-handler.middleware';
import { databaseConnection, seedDatabase, databaseCleanUp } from '@common/config/inversify.config';
import notFoundMiddleware from '@common/middleware/not-found.middleware';
import '@v1/controllers/admin/admin.controller';
import '@v1/controllers/admin/audit-log.controller';
import '@v1/controllers/admin/business-type.controller';
import '@v1/controllers/admin/commission-setting.controller';
import '@v1/controllers/admin/dashboard.controller';
import '@v1/controllers/admin/invoice.controller';
import '@v1/controllers/admin/merchant.controller';
import '@v1/controllers/admin/potential-customer.controller';
import '@v1/controllers/admin/service.controller';
import '@v1/controllers/admin/subscription-plan.controller';
import '@v1/controllers/auth.controller';
import '@v1/controllers/business-type.controller';
import '@v1/controllers/invoice.controller';
import '@v1/controllers/merchant-account.controller';
import '@v1/controllers/order.controller';
import '@v1/controllers/potential-customer.controller';
import '@v1/controllers/reminder.controller';
import '@v1/controllers/service.controller';
import '@v1/controllers/subscription-plan.controller';
import '@v1/controllers/user.controller';
import '@v1/controllers/virtual-account.controller';

const port = env.SERVER_PORT;

const server: any = new InversifyExpressServer(container);

server.setConfig((app: any) => {
  app.use(express.json());

  app.use(express.urlencoded({ extended: true }));

  app.use(cors());

  app.use(responseHandlerMiddleware());

  app.use(helmet());
});

databaseCleanUp.deleteExpiredEntries();
databaseConnection.connect();
pictureUploadCleanUp.emptyUploadFolder();
merchantReminder.updateReminders();
merchantSubscriptionsCleanup.expireMerchantSubscriptions();
seedDatabase.run();

let app = server.build();

app.use('*', notFoundMiddleware);

app.listen(port);

logger.info(`Server started and listening on port ${port}, at ${new Date().toISOString()}`);

exports = module.exports = app;
