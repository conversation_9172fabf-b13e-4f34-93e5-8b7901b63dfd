import BusinessTypeRepository from '@v1/repositories/business-type.repository';
import dayjs from 'dayjs';

export async function getModifiedUser(user: any) {
  return {
    _id: user._id,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.phoneNumber,
    email: user.email,
    profilePicture: user.profilePicture,
    role: user.role,
    isVerified: user.isVerified,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt,
  };
}

export async function getModifiedMerchant(user: any, merchant: any) {
  const businessType = await BusinessTypeRepository.getById(merchant.businessType.toString());

  return {
    user: {
      _id: user._id,
      role: user.role,
      lastLoggedInAt: user.lastLoggedInAt,
      isVerified: user.isVerified,
      verifiedAt: user.verifiedAt,
      profilePicture: user.profilePicture,
    },
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.phoneNumber,
    email: user.email,
    address: merchant.address,
    state: merchant.state,
    businessType: {
      name: businessType?.name || null,
    },
    businessName: merchant.businessName,
    status: merchant.status,
    openingTime: merchant.openingTime,
    closingTime: merchant.closingTime,
    openingDays: merchant.openingDays,
    instagramHandle: merchant.instagramHandle,
    twitterHandle: merchant.twitterHandle,
    facebookUsername: merchant.facebookUsername,
    subscriptionPlan: merchant.subscriptionPlan,
    createdAt: merchant.createdAt,
    updatedAt: merchant.updatedAt,
  };
}

export const getModifiedSubscriptionData = (data: any) => {
  const modifiedStartDate: Date = new Date(dayjs(data.startDate).add(1, 'h').toISOString());
  const modifiedEndDate: Date = new Date(dayjs(data.endDate).add(1, 'h').toISOString());
  return {
    _id: data._id,
    subscriptionPlan: data.subscriptionPlan,
    merchant: data.merchant,
    startDate: modifiedStartDate,
    endDate: modifiedEndDate,
    status: data.status,
    createdAt: data.createdAt,
    updatedAt: data.updatedAt,
  };
}