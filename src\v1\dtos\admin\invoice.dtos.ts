import { z as schema } from 'zod';

const toggleMerchantSubscriptionSuspension = schema.object({
  body: schema
    .object({
      subscriptionId: schema.string(),
    })
    .strict(),
});

const merchantId = schema.object({
  params: schema
    .object({
      merchant_id: schema.string(),
    })
    .strict(),
});

const subscriptionId = schema.object({
  params: schema
    .object({
      subscription_id: schema.string(),
    })
    .strict(),
});

export const AdminInvoiceSchema = {
  toggleMerchantSubscriptionSuspension,
  merchantId,
  subscriptionId,
};
