import { CRUD } from '@common/interfaces/crud.interface';
import { injectable, unmanaged } from 'inversify';
import { Model as MongooseModel } from 'mongoose';
import 'reflect-metadata';

@injectable()
class BaseRepository implements CRUD {
  private model: MongooseModel<any>;

  constructor(@unmanaged() model: MongooseModel<any>) {
    this.model = model;
  }

  async create(data: any) {
    return await this.model.create(data);
  }

  async list(page: number = 1, limit: number = 0, startDate: Date = new Date(1970), endDate: Date = new Date()) {
    const data = await this.model
      .find({ createdAt: { $gte: startDate, $lte: endDate } })
      .limit(limit)
      .skip(limit * (page - 1))
      .sort('-createdAt')
      .where('isDeleted')
      .equals(false)
      .exec();

    const filter = { createdAt: { $gte: startDate, $lte: endDate } };
    return await this.getPaginationDetails(filter, limit, page, data);
  }

  async getById(id: string) {
    let user = await this.model.findById(id).where('isDeleted').equals(false).exec();

    return user;
  }

  async update(resource: any) {
    const { _id, ...data } = resource;
    return await this.model.findByIdAndUpdate(_id, { $set: data }, { new: true }).where('isDeleted').equals(false).exec();
  }

  async updateDeleteStatus(id: string, userId: string | null = null) {
    if (userId) {
      return await this.model
        .findOneAndUpdate({ _id: id, user: userId, isDeleted: false }, { $set: { isDeleted: true, deletedAt: new Date() } }, { new: true })
        .select(['+isDeleted', '+deletedAt', '+verifiedAt'])
        .exec();
    }
    return await this.model
      .findByIdAndUpdate(id, { $set: { isDeleted: true, deletedAt: new Date() } }, { new: true })
      .where('isDeleted')
      .equals(false)
      .select(['+isDeleted', '+deletedAt', '+verifiedAt'])
      .exec();
  }

  async delete(id: string) {
    return await this.model.findByIdAndDelete(id).exec();
  }

  async getPaginationDetails(filter: any, limit: number, page: number, data: any[]) {
    const numberOfDocuments = await this.model.countDocuments(filter);
    const numberOfPages = Math.ceil(numberOfDocuments / limit);

    return { data, pageLimit: +limit, currentPage: +page, numberOfPages: numberOfPages || 1 };
  }

  async getTotalDocuments() {
    return await this.model.countDocuments();
  }

  async deleteMany(merchantId: string) {
    return await this.model.deleteMany({ user: merchantId }).exec();
  }
}

export default BaseRepository;
