import CustomError from '@common/helpers/custom-error.helper';
import TYPES from '@common/types/inversify.types';
import { CreateMerchantSubscription } from '@common/types/invoice.types';
import { InvoiceRepository } from '@v1/repositories/invoice.repository';
import { SubscriptionPlanRepository } from '@v1/repositories/subscription-plan.repository';
import { UserRepository } from '@v1/repositories/user.repository';
import dayjs from 'dayjs';
import { inject, injectable } from 'inversify';
import mongoose from 'mongoose';
import cron from 'node-cron';

@injectable()
class InvoiceService {
  constructor(
    @inject(TYPES.InvoiceRepository) private invoiceRepository: InvoiceRepository,
    @inject(TYPES.SubscriptionPlanRepository) private subscriptionPlanRepository: SubscriptionPlanRepository,
    @inject(TYPES.UserRepository) private userRepository: UserRepository,
  ) {}

  getInvoiceDetails(invoice: any) {
    const invoiceDetails: any = {};
    Object.keys(invoice).forEach((key: string) => {
      if (key === 'invoiceId') {
        invoiceDetails['_id'] = invoice[key];
      } else if (key === 'merchantId') {
        invoiceDetails['user'] = invoice[key];
      } else if (key === 'subscriptionPlanId') {
        invoiceDetails['subscriptionPlan'] = invoice[key];
      } else {
        invoiceDetails[key] = invoice[key];
      }
    });

    return invoiceDetails;
  }

  async getModifiedSubscriptionData(invoice: any, invoiceCreation: boolean = false) {
    const modifiedStartDate: Date = new Date(dayjs(invoice.startDate).add(1, 'h').toISOString());
    const modifiedEndDate: Date = new Date(dayjs(invoice.endDate).add(1, 'h').toISOString());
    const subscription = invoiceCreation && (await this.subscriptionPlanRepository.getById(invoice.subscriptionPlan));
    return {
      _id: invoice._id,
      subscriptionPlan: {
        name: invoiceCreation ? subscription.name : invoice.subscriptionPlan.name,
        amount: invoiceCreation ? subscription.amountPerMonth : invoice.subscriptionPlan.amountPerMonth,
      },
      startDate: modifiedStartDate,
      endDate: modifiedEndDate,
      status: invoice.status,
      createdAt: invoice.createdAt,
      updatedAt: invoice.updatedAt,
    };
  }

  async createMerchantSubscription(resource: CreateMerchantSubscription) {
    if (
      !mongoose.Types.ObjectId.isValid(resource.subscriptionPlanId) ||
      !(await this.subscriptionPlanRepository.getSubscriptionPlanById(resource.subscriptionPlanId))
    ) {
      throw new CustomError(404, 'Subscription plan does not exist');
    }

    const merchantSubscription = await this.invoiceRepository.getMerchantActiveSubscription(resource.merchantId);

    if (merchantSubscription) {
      throw new CustomError(409, 'Merchant has an active subscription!');
    }

    const user = await this.userRepository.getById(resource.merchantId);
    resource.merchant = user.merchant;
    resource.startDate = new Date();
    resource.endDate = new Date(dayjs(resource.startDate).add(1, 'M').toISOString());
    const invoiceDetails = this.getInvoiceDetails(resource);
    const createdInvoice = await this.invoiceRepository.create(invoiceDetails);
    return await this.getModifiedSubscriptionData(createdInvoice, true);
  }

  async getMerchantActiveSubscription(merchantId: string) {
    const merchantSubscription = await this.invoiceRepository.getMerchantActiveSubscription(merchantId);
    if (!merchantSubscription) {
      throw new CustomError(404, 'No active subscription found for merchant');
    }
    return await this.getModifiedSubscriptionData(merchantSubscription);
  }

  async expireMerchantSubscriptions() {
    cron.schedule(
      '* * * * *',
      async () => {
        await this.invoiceRepository.expireMerchantSubscriptions();
      },
      {
        scheduled: true,
        timezone: 'Africa/Lagos',
      },
    );
  }
}

export { InvoiceService };
