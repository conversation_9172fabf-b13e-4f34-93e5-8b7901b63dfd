import { UserRole } from '@common/enums/index.enums';
import CustomError from '@common/helpers/custom-error.helper';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { OrderSchema } from '@v1/dtos/order.dtos';
import { OrderRepository } from '@v1/repositories/order.repository';
import { OrderService } from '@v1/services/order.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpDelete, httpGet, httpPatch, httpPost, request, requestParam, response } from 'inversify-express-utils';
import jwt from 'jsonwebtoken';

@controller('/orders', Auth.validateAuth([UserRole.MERCHANT]))
class OrderController {
  constructor(
    @inject(TYPES.OrderService) private orderService: OrderService,
    @inject(TYPES.OrderRepository) private orderRepository: OrderRepository,
  ) {}

  @httpPost('', validate(OrderSchema.createOrder))
  public async createOrder(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const order = await this.orderService.createOrder({ merchantId: res.locals.jwt._id, ...req.body });
      res.handleSuccessResponse(201, 'Order created', order);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('')
  public async getAllOrders(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchantOrders = await this.orderService.getOrders(res.locals.jwt._id);

      res.handleSuccessResponse(200, 'Orders retrieved', merchantOrders);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/:order_id', validate(OrderSchema.orderId))
  public async getOrderById(@requestParam('order_id') orderId: string, @response() res: CustomResponse) {
    try {
      const order = await this.orderService.getOrder({ merchantId: res.locals.jwt._id, orderId });
      res.handleSuccessResponse(200, `Order retrieved`, order);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete('/:order_id', validate(OrderSchema.orderId))
  public async deleteOrderById(@requestParam('order_id') orderId: string, @response() res: CustomResponse) {
    try {
      await this.orderService.cancelOrder(orderId, res.locals.jwt._id);
      res.handleSuccessResponse(200, 'Order cancelled');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('', validate(OrderSchema.updateOrder))
  public async updateOrderById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const order = await this.orderService.updateOrderById({ merchantId: res.locals.jwt._id, ...req.body });

      res.handleSuccessResponse(200, `Order updated`, order);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/:order_id/status', validate(OrderSchema.orderId))
  public async markOrderAsCompleted(@requestParam('order_id') orderId: string, @response() res: CustomResponse) {
    try {
      const order = await this.orderService.markOrderAsCompleted(orderId, res.locals.jwt._id);

      res.handleSuccessResponse(200, `Order completed`, order);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
