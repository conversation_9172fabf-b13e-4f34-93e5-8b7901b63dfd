import multer from 'multer';
import express from 'express';
import CustomError from '@common/helpers/custom-error.helper';

const multerStorage = multer.diskStorage({
  destination: (req: express.Request, file: any, cb: any) => {
    cb(null, 'uploads/');
  },
  filename: (req: express.Request, file: any, cb: any) => {
    const fileExtension = file.mimetype.split('/')[1];
    cb(null, `${file.fieldname}-${Date.now()}-${Math.round(Math.random() * 1e9)}.${fileExtension}`);
  },
});

const multerFilter = (req: express.Request, file: any, cb: any) => {
  const fileExtension = file.mimetype.split('/')[1];

  if (fileExtension === 'jpg' || fileExtension === 'jpeg' || fileExtension === 'png') {
    cb(null, true);
  } else {
    cb(null, false);
    cb(new CustomError(400, 'Unsupported image file format (Try jpg, jpeg or png)'));
  }
};

export const uploadProfilePicture = multer({
  storage: multerStorage,
  fileFilter: multerFilter,
}).single('profilePicture');

export const uploadReviewImage = multer({
  storage: multerStorage,
  fileFilter: multerFilter,
}).single('reviewImage');
