import express from 'express';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import { UserRepository } from '@v1/repositories/user.repository';
import { UserService } from '@v1/services/user.service';
import { controller, httpGet, httpPost, httpPut, httpDelete, request, response, httpPatch, requestParam } from 'inversify-express-utils';
import TYPES from '@common/types/inversify.types';
import { inject } from 'inversify';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import { UserSchema } from '@v1/dtos/user.dtos';
import CustomError from '@common/helpers/custom-error.helper';
import { genericFilter } from '@v1/dtos/common.dtos';
import { UserRole } from '@common/enums/index.enums';

@controller('')
class UserController {
  private userService;
  private userRepository;

  constructor(@inject(TYPES.UserService) userService: UserService, @inject(TYPES.UserRepository) userRepository: UserRepository) {
    this.userService = userService;
    this.userRepository = userRepository;
  }

  @httpGet('/users', validate(genericFilter))
  public async listUsers(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const allUsers = await this.userService.getUsers(req.query);
      res.handleSuccessResponse(200, 'Users retrieved', allUsers);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/user', validate(UserSchema.updateUser))
  public async updateUserById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const user = await this.userService.updateUserById(req.body);

      if (!user) {
        throw new CustomError(404, 'User not found');
      }

      res.handleSuccessResponse(200, `User updated`, user);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/user/:user_id', validate(UserSchema.userId))
  public async getUserById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const user = await this.userRepository.getById(req.params.user_id);

      if (!user) {
        throw new CustomError(404, 'User not found');
      }

      res.handleSuccessResponse(200, `User retrieved`, user);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete('/user/:user_id', Auth.validateAuth([UserRole.SUPER_ADMIN]), validate(UserSchema.userId))
  public async deleteUserById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const user = await this.userService.deleteUserById(req.params.user_id);

      if (!user) {
        throw new CustomError(404, 'User not found');
      }

      res.handleSuccessResponse(200, 'User deleted');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
