import { UserRole } from '@common/enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { AdminInvoiceSchema } from '@v1/dtos/admin/invoice.dtos';
import { _idBody } from '@v1/dtos/common.dtos';
import { logAudit } from '@v1/middleware/admin.middleware';
import { AdminInvoiceService } from '@v1/services/admin/invoice.service';
import { InvoiceService } from '@v1/services/invoice.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpDelete, httpGet, httpPatch, httpPost, request, requestParam, response } from 'inversify-express-utils';

@controller('/admin')
class AdminInvoiceController {
  constructor(@inject(TYPES.AdminInvoiceService) private adminInvoiceService: AdminInvoiceService) {}

  @httpGet('/invoice/subscriptions', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]))
  public async getSubscriptions(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const merchantSubscriptions = await this.adminInvoiceService.getSubscriptions(req.query);
      res.handleSuccessResponse(200, 'Merchant subscriptions retrieved successfully', merchantSubscriptions);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet(
    '/invoice/subscriptions/:subscription_id',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    validate(AdminInvoiceSchema.subscriptionId),
  )
  public async getSubscriptionById(@requestParam('subscription_id') subscriptionId: string, @response() res: CustomResponse) {
    try {
      const subscription = await this.adminInvoiceService.getSubscriptionById(subscriptionId);
      res.handleSuccessResponse(200, 'Merchant subscription retrieved successfully', subscription);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet(
    '/merchant/:merchant_id/invoice/subscriptions',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]),
    validate(AdminInvoiceSchema.merchantId),
  )
  public async getSubscriptionByMerchantId(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const subscriptions = await this.adminInvoiceService.getSubscriptionsByMerchantId({ merchantId: req.params.merchant_id, ...req.query });
      res.handleSuccessResponse(200, 'Merchant subscriptions retrieved successfully', subscriptions);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete(
    '/invoice/subscriptions/:subscription_id',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminInvoiceSchema.subscriptionId),
    logAudit('Merchant subscription deletion'),
  )
  public async deleteMerchantSubscriptionById(@requestParam('subscription_id') subscriptionId: string, @response() res: CustomResponse) {
    try {
      await this.adminInvoiceService.deleteMerchantSubscription(subscriptionId);
      res.handleSuccessResponse(200, 'Merchant subscription deleted successfully');
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/invoice/subscriptions/suspend',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminInvoiceSchema.toggleMerchantSubscriptionSuspension),
    logAudit('Merchant subscription suspension'),
  )
  public async suspendMerchantSubscription(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const updatedSubscription = await this.adminInvoiceService.toggleMerchantSubscriptionSuspension(req.body.subscriptionId, true);
      res.handleSuccessResponse(200, 'Merchant subscription suspended successfully', updatedSubscription);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch(
    '/invoice/subscriptions/unsuspend',
    Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN]),
    validate(AdminInvoiceSchema.toggleMerchantSubscriptionSuspension),
    logAudit('Merchant subscription suspension lift'),
  )
  public async unsuspendMerchantSubscription(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const updatedSubscription = await this.adminInvoiceService.toggleMerchantSubscriptionSuspension(req.body.subscriptionId);
      res.handleSuccessResponse(200, 'Merchant subscription unsuspended successfully', updatedSubscription);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
}
