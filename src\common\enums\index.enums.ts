export enum AdminStatus {
  ACTIVE = 'ACTIVE',
  NOT_VERIFIED = 'NOT_VERIFIED',
  SUSPENDED = 'SUSPENDED',
  BLOCKED = 'BLOCKED',
}

export enum AuthTokenType {
  REFRESH_TOKEN = 'REFRESH_TOKEN',
}

export enum CommissionType {
  FLAT_RATE = 'FLAT_RATE',
  PERCENTAGE = 'PERCENTAGE',
}

export enum MerchantDefaultSetting {
  COUNTRY = 'NIGERIA',
}

export enum MerchantStatus {
  ACTIVE = 'ACTIVE',
  NOT_VERIFIED = 'NOT_VERIFIED',
  SUSPENDED = 'SUSPENDED',
  BLOCKED = 'BLOCKED',
}

export enum OtpValidationType {
  ACCOUNT_VERIFICATION = 'ACCOUNT_VERIFICATION',
  PASSWORD_RESET = 'PASSWORD_RESET',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  EMAIL_CHANGE = 'EMAIL_CHANGE',
}

export enum RequestMethods {
  GET = 'GET',
  POST = 'POST',
}

export enum ServiceDefaultSetting {
  reviewImageUrl = 'https://res.cloudinary.com/wadoo/image/upload/v1667057006/defaults/istockphoto-**********-612x612_sjxdgz.jpg',
}

export enum ServiceStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export enum SubscriptionStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  EXPIRED = 'EXPIRED',
}

export enum UserDefaultSetting {
  profilePictureUrl = 'https://res.cloudinary.com/wadoo/image/upload/v1667041260/defaults/blank-profile-picture-973460_1280_sr3fib.png',
}

export enum UserRole {
  MERCHANT = 'MERCHANT',
  ADMIN = 'ADMIN',
  SUPER_ADMIN = 'SUPER_ADMIN',
  ANALYST = 'ANALYST',
}
