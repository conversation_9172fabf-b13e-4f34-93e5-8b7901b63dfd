import { BusinessTypes } from '@common/types/admin.types';
import TYPES from '@common/types/inversify.types';
import { BusinessTypeRepository } from '@v1/repositories/business-type.repository';
import { injectable, inject } from 'inversify';
import 'reflect-metadata';

@injectable()
class BusinessTypeService {
  private businessTypeRepository;

  constructor(@inject(TYPES.BusinessTypeRepository) businessTypeRepository: BusinessTypeRepository) {
    this.businessTypeRepository = businessTypeRepository;
  }

  async getBusinessTypes() {
    return await this.businessTypeRepository.getBusinessTypes();
  }
}

export { BusinessTypeService };
