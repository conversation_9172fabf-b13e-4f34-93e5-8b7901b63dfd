import { AnyZodObject, ZodError } from 'zod';

export const validateInput = (schema: AnyZodObject, input: any) => {
  try {
    const data = schema.parse(input);
    return { success: true, data };
  } catch (error) {
    let errorMessage: string = '';
    if (error instanceof ZodError) {
      const errorMessageArr: string[] = error.issues.map(issue => `${issue.path.at(-1)}: ${issue.message.toLowerCase()}`);
      errorMessage = errorMessageArr.join(', ') + '.';
    }
    return { success: false, errorMessage };
  }
};
