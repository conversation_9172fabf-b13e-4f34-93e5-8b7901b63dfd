import { UserRole } from '@common/enums/index.enums';
import Auth from '@common/middleware/auth.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { MerchantSchema } from '@v1/dtos/merchant.dtos';
import { ReminderService } from '@v1/services/reminder.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, request, response } from 'inversify-express-utils';
import { CustomResponse } from '@common/interfaces/custom-response.interface';

@controller('', Auth.validateAuth([UserRole.MERCHANT]))
class ReminderController {
  private reminderService;

  constructor(@inject(TYPES.ReminderService) reminderService: ReminderService) {
    this.reminderService = reminderService;
  }

  @httpGet('/merchant/:merchant_id/reminders', validate(MerchantSchema.merchantId))
  public async getOrdersByMerchantId(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const reminders = await this.reminderService.getReminders(req.params.merchant_id);

      res.handleSuccessResponse(200, `Merchant's reminders retrieved`, reminders);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
