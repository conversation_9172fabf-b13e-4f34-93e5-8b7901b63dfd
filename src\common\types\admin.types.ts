import { CommissionType } from '@common/enums/index.enums';

type AdminRole = 'ADMIN' | 'ANALYST';

export type ProspectAdmin = {
  email: String;
  role: AdminRole;
};

export type Admin = {
  email: String;
  firstName: String;
  lastName: String;
  phoneNumber: String;
  password: String;
};

export type BusinessTypes = {
  businessTypes: string[];
};

export type CreatePotentialCustomers = {
  potentialCustomers: object[];
};

export type Commission = {
  commissionType: CommissionType;
  flatRate?: string | null;
  percentage?: string | null;
};

export type PopulatedAdminAccount = {
  user: {
    _id: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
    role: string;
    lastLoggedInAt: string;
    profilePicture: {
      id: string | null;
      url: string | null;
    };
    isVerified: string;
    verifiedAt: string;
  };
  status: string;
  createdAt: string;
  updatedAt: string;
};
