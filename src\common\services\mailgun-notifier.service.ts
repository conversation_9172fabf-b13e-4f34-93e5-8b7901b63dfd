import env from '@common/config/env.config';
import { EmailNotifier } from '@common/interfaces/email-notifier.interface';
import { injectable } from 'inversify';
import 'reflect-metadata';
import formData from 'form-data';
import Mailgun, { MailgunClientOptions } from 'mailgun.js';

@injectable()
class MailgunNotifier implements EmailNotifier {
  protected mg: any;
  protected from = 'Wadoo <<EMAIL>>';
  protected subject: string | null = null;
  protected template: string | null = null;
  protected mailVariables: string | null = null;

  constructor() {
    let mailgunSetup: MailgunClientOptions = { username: env.MAILGUN_USERNAME || '', key: env.MAILGUN_KEY || '' };
    let mailgun = new Mailgun(formData);

    this.mg = mailgun.client(mailgunSetup);
  }

  async setMailProperties(subject: string, mailTemplate: string, mailVariables?: any) {
    this.subject = subject;
    this.template = mailTemplate;
    this.mailVariables = mailVariables || {};
  }

  async send(recipient: string): Promise<any> {
    const data = {
      from: this.from,
      to: recipient,
      subject: this.subject,
      template: this.template,
      'h:X-Mailgun-Variables': JSON.stringify(this.mailVariables),
    };
    await this.mg.messages.create(env.MAILGUN_DOMAIN, data);
  }
}

export default MailgunNotifier;
