import { UserRole } from '@common/enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import TYPES from '@common/types/inversify.types';
import { SubscriptionPlanService } from '@v1/services/subscription-plan.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpGet, request, response } from 'inversify-express-utils';

@controller('/subscription-plans', Auth.validateAuth([UserRole.MERCHANT]))
class SubscriptionPlanController {
  constructor(@inject(TYPES.SubscriptionPlanService) private subscriptionPlanService: SubscriptionPlanService) {}

  @httpGet('/')
  public async getSubscriptionPlans(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const subscriptionPlans = await this.subscriptionPlanService.getSubscriptionPlans();
      res.handleSuccessResponse(200, 'Subscription plans retrieved successfully', subscriptionPlans);
    } catch (error) {
      res.handleErrorResponse(error);
    }
  }
}
