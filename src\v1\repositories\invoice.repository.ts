import { Invoice } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import BaseRepository from './base.repository';
import { SubscriptionStatus } from '@common/enums/index.enums';

@injectable()
class InvoiceRepository extends BaseRepository {
  constructor() {
    super(Invoice);
  }

  async getMerchantActiveSubscription(id: string) {
    return await Invoice.findOne({ user: id, status: SubscriptionStatus.ACTIVE, isDeleted: false })
      .populate('subscriptionPlan', '-_id name amountPerMonth')
      .select('-__v -merchant')
      .exec();
  }

  async getSubscriptionById(id: string) {
    return await Invoice.findOne({ _id: id, isDeleted: false })
      .populate('subscriptionPlan', '-_id name amountPerMonth')
      .populate({ path: 'merchant', select: ['firstName', 'lastName'] })
      .select('-__v')
      .exec();
  }

  async findMerchantSubscriptions(filter: any, page: number, limit: number) {
    return await Invoice.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate('subscriptionPlan', '-_id name amountPerMonth')
      .populate('merchant', '-_id firstName lastName')
      .select('-__v')
      .sort('-createdAt')
      .exec();
  }

  async getSubscriptions(page: number = 1, limit: number = 10, startDate: Date = new Date(1970), endDate: Date = new Date()) {
    const filter = {
      createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isDeleted: false,
    };
    const merchantSubscriptions = await this.findMerchantSubscriptions(filter, page, limit);
    return await this.getPaginationDetails(filter, limit, page, merchantSubscriptions);
  }

  async getSubscriptionsByMerchantId(
    merchantId: string,
    page: number = 1,
    limit: number = 10,
    startDate: Date = new Date(1970),
    endDate: Date = new Date(),
  ) {
    const filter = {
      createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isDeleted: false,
      user: merchantId,
    };
    const merchantSubscriptions = await this.findMerchantSubscriptions(filter, page, limit);
    return await this.getPaginationDetails(filter, limit, page, merchantSubscriptions);
  }

  async updateSubscriptionById({ id, ...data }: any) {
    return await Invoice.findOneAndUpdate({ _id: id, isDeleted: false }, data, { new: true })
      .populate('subscriptionPlan', '-_id name amountPerMonth')
      .populate({ path: 'merchant', select: ['firstName', 'lastName'] })
      .select('-__v')
      .exec();
  }

  async expireMerchantSubscriptions() {
    return await Invoice.updateMany({ endDate: { $lte: new Date() }, isDeleted: false }, { status: SubscriptionStatus.EXPIRED }).exec();
  }
}

export { InvoiceRepository };
