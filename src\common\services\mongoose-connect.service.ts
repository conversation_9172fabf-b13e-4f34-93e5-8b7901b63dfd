import { injectable } from 'inversify';
import 'reflect-metadata';
import mongoose from 'mongoose';
import logger from '@common/logger';
import config from '@common/config/env.config';
import { DatabaseConnection } from '@common/interfaces/database-connection.interface';

@injectable()
class MongooseService implements DatabaseConnection {
  private databaseUrl = config.MONGODB_DATABASE_URL;
  private numberOfConnectionTries: number = 0;
  private numberOfSecondsBeforeRetry: number = 5;
  private maximumNumberOfConnectionTrials: number = 10;
  private mongooseOptions = { serverSelectionTimeoutMS: 5000 };

  constructor() {}

  async disconnect(): Promise<void> {
    mongoose.disconnect();
  }

  async connect(): Promise<void> {
    logger.info('Attempting Mongoose connection to MongoDB (will retry if connection fails)');
    try {
      await mongoose.connect(this.databaseUrl || '', this.mongooseOptions);
      logger.info('MongoDB is connected');
    } catch (error) {
      this.numberOfConnectionTries++;

      logger.error(`Mongoose connection to MongoDB unsuccessful, number of tries made : ${this.numberOfConnectionTries}`, error);

      if (this.numberOfConnectionTries < this.maximumNumberOfConnectionTrials) {
        setTimeout(this.connect, this.numberOfSecondsBeforeRetry * 1000);
      }
    }
  }
}

export default MongooseService;
