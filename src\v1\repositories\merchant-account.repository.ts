import { MerchantAccount } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class MerchantAccountRepository extends BaseRepository {
  constructor() {
    super(MerchantAccount);
  }

  async findMerchants(filter: any, limit: number, page: number) {
    return await MerchantAccount.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate({ path: 'user', select: ['isVerified', 'verifiedAt', 'role', 'lastLoggedInAt', 'profilePicture'] })
      .populate('businessType', '-createdAt -updatedAt -_id -__v')
      .select('-_id -__v')
      .sort('-createdAt')
      .exec();
  }

  async deleteByUserId(userId: string) {
    return await MerchantAccount.findOneAndDelete({ user: userId }).exec();
  }

  async updateByUserId(resource: any) {
    const { userId, ...data } = resource;
    return await MerchantAccount.findOneAndUpdate({ user: userId }, { $set: data }, { new: true })
      .populate({ path: 'user', select: ['isVerified', 'verifiedAt', 'role', 'lastLoggedInAt', 'profilePicture'] })
      .populate('businessType', '-createdAt -updatedAt -_id -__v')
      .select('-_id -__v')
      .exec();
  }

  async getByEmail(email: string) {
    return await MerchantAccount.findOne({ email }).exec();
  }

  async getByUserId(userId: string) {
    return await MerchantAccount.findOne({ user: userId })
      .populate({ path: 'user', select: ['isVerified', 'verifiedAt', 'role', 'lastLoggedInAt', 'profilePicture'] })
      .populate('businessType', '-createdAt -updatedAt -_id -__v')
      .select('-_id -__v -service -merchantCustomer -order')
      .exec();
  }

  async getMerchants(
    page: number = 1,
    limit: number = 10,
    startDate: Date = new Date(1970),
    endDate: Date = new Date(),
    status: string | null = null,
  ) {
    let data, filter;

    if (status) {
      filter = { createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) }, status: status.toUpperCase() };

      data = await this.findMerchants(filter, limit, page);

      return await this.getPaginationDetails(filter, limit, page, data);
    }

    filter = { createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) } };

    data = await this.findMerchants(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }

  async search(page: number = 1, limit: number = 10, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');

    const filter = { $or: [{ email: regex }, { businessName: regex }, { firstName: regex }, { lastName: regex }] };

    const data = await this.findMerchants(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }
}

export { MerchantAccountRepository };

export default new MerchantAccountRepository();
