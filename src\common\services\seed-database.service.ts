import { databaseSeeder } from '@common/config/inversify.config';
import { BusinessType } from '@common/schema/index.schema';

export class SeedDatabase {
  async run() {
    await databaseSeeder.seed(BusinessType, [
      { name: 'Electricians' },
      { name: '<PERSON>' },
      { name: '<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>, <PERSON> Seller, Barber, Makeup Artist, Nail Technician)' },
      { name: 'Supplier (water seller or supplier, cooking gas seller, grocery seller)' },
      { name: 'Home Cleaning Service' },
      { name: 'Baking Service' },
      { name: 'Catering Service (Food & Drinks)' },
      { name: 'Fashion Designer' },
      { name: 'Recharge Card' },
      { name: 'Spa & Masseuse' },
      { name: 'Plumber' },
      { name: '<PERSON>' },
      { name: 'Inverter Technician' },
      { name: 'Cable TV Technician (DSTV)' },
      { name: 'Event Planners' },
      { name: 'Refuse Collector or Waste Management Service' },
      { name: 'Personal Trainer' },
      { name: 'Home Lesson Teachers (music, language, academic subjects, etc)' },
      { name: 'Monthly Savings Service (Esusu)' },
      { name: 'Fumigation Service' },
    ]);
  }
}
