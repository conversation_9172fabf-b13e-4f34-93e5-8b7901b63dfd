import { injectable, inject } from 'inversify';
import 'reflect-metadata';
import TYPES from '@common/types/inversify.types';
import { CommissionSettingRepository } from '@v1/repositories/commission-setting.repository';
import { Commission } from '@common/types/admin.types';
import { CommissionType } from '@common/enums/index.enums';

@injectable()
class CommissionSettingService {
  private commissionSettingRepository;

  constructor(@inject(TYPES.CommissionSettingRepository) commissionSettingRepository: CommissionSettingRepository) {
    this.commissionSettingRepository = commissionSettingRepository;
  }

  async setCommission(resource: Commission) {
    if (resource.flatRate) {
      resource.commissionType = CommissionType.FLAT_RATE;

      resource.percentage = null;

      const commission = await this.commissionSettingRepository.setCommission(resource);

      return {
        commissionType: commission.commissionType,
        flatRate: commission.flatRate,
      };
    }

    if (resource.percentage) {
      resource.commissionType = CommissionType.PERCENTAGE;

      resource.flatRate = null;

      const commission = await this.commissionSettingRepository.setCommission(resource);

      return {
        commissionType: commission.commissionType,
        percentage: commission.percentage,
      };
    }
  }

  async getCommission() {
    const commission: any = await this.commissionSettingRepository.list();

    if (commission) {
      return {
        commissionType: commission.data[0].commissionType,
        flatRate: commission.data[0].flatRate,
        percentage: commission.data[0].percentage,
      };
    }
  }
}

export { CommissionSettingService };
