import { injectable } from 'inversify';
import 'reflect-metadata';
import { PotentialCustomer } from '../../common/schema/index.schema';
import BaseRepository from './base.repository';

@injectable()
class PotentialCustomerRepository extends BaseRepository {
  constructor() {
    super(PotentialCustomer);
  }

  async getPotentialCustomersByMerchantId(
    merchantId: string,
    page: number = 1,
    limit: number = 10,
    startDate: Date = new Date(1970),
    endDate: Date = new Date(),
  ) {
    const filter = {
      createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isDeleted: false,
      user: merchantId,
    };

    const customers = await this.findPotentialCustomers(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, customers);
  }

  async getPotentialCustomerById(_id: string) {
    return await PotentialCustomer.findOne({ _id, isDeleted: false })
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -email -user -subscriptionPlan -firstName -walletId -walletClientId -walletAccountNumber -lastName -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .populate('serviceRendered', '-_id -duration -__v -createdAt -reminderFrequency -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user')
      .exec();
  }

  async updatePotentialCustomerById(resource: any) {
    const { _id, ...data } = resource;
    return await PotentialCustomer.findOneAndUpdate({ _id, isDeleted: false }, { $set: data }, { new: true })
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -email -user -subscriptionPlan -firstName -walletId -walletClientId -walletAccountNumber -lastName -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .populate('serviceRendered', '-_id -duration -__v -createdAt -reminderFrequency -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user')
      .exec();
  }

  async findPotentialCustomers(filter: any, limit: number = 1, page: number = 1) {
    return await PotentialCustomer.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -email -user -subscriptionPlan -firstName -walletId -walletClientId -walletAccountNumber -lastName -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .populate('serviceRendered', '-_id -duration -__v  -createdAt -reminderFrequency -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user')
      .sort('-createdAt')
      .exec();
  }

  async getPotentialCustomers(page: number = 1, limit: number = 10, startDate: Date = new Date(1970), endDate: Date = new Date()) {
    const filter = {
      createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isDeleted: false,
    };

    const services = await this.findPotentialCustomers(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, services);
  }

  async search(page: number = 1, limit: number = 10, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');

    const filter = { $and: [{ $or: [{ email: regex }, { name: regex }] }, { isDeleted: false }] };

    const data = await this.findPotentialCustomers(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }

  async searchByMerchantId(merchantId: string, page: number, limit: number = 30, searchWord: string) {
    const regex = new RegExp(searchWord, 'i');

    const filter = { $and: [{ $or: [{ email: regex }, { name: regex }] }, { user: merchantId, isDeleted: false }] };

    const data = await this.findPotentialCustomers(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, data);
  }
}

export { PotentialCustomerRepository };
