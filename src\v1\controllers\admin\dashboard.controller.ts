import express from 'express';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import { DashboardService } from '@v1/services/admin/dashboard.service';
import { controller, httpGet, httpPost, httpPut, httpDelete, request, response, httpPatch, requestParam } from 'inversify-express-utils';
import { inject } from 'inversify';
import TYPES from '@common/types/inversify.types';
import Auth from '@common/middleware/auth.middleware';
import { UserRole } from '@common/enums/index.enums';

@controller('/admin/dashboard', Auth.validateAuth([UserRole.SUPER_ADMIN, UserRole.ADMIN, UserRole.ANALYST]))
class DashboardController {
  private dashboardService;

  constructor(@inject(TYPES.DashboardService) dashboardService: DashboardService) {
    this.dashboardService = dashboardService;
  }

  @httpGet('/')
  public async getDashboardInfo(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const data = await this.dashboardService.getDashboardInfo();
      res.handleSuccessResponse(200, 'Data retrieved', data);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
