import { z as schema } from 'zod';

const createOrder = schema.object({
  body: schema
    .object({
      // merchantId: schema.string(),
      name: schema.string(),
      serviceId: schema.string(),
      date: schema.string(),
      time: schema.string().optional(),
      price: schema.number(),
      phoneNumber: schema.string(),
      isFullDay: schema.boolean(),
    })
    .strict(),
});

const updateOrder = schema.object({
  body: schema
    .object({
      orderId: schema.string(),
      name: schema.string().optional(),
      serviceId: schema.string().optional(),
      date: schema.string().optional(),
      time: schema.string().optional(),
      price: schema.string().optional(),
      phoneNumber: schema.string().optional(),
      isFullDay: schema.boolean().optional(),
    })
    .strict(),
});

const orderId = schema.object({
  params: schema
    .object({
      order_id: schema.string(),
    })
    .strict(),
});

export const OrderSchema = {
  orderId,
  updateOrder,
  createOrder,
};
