import TYPES from '@common/types/inversify.types';
import { OrderRepository } from '@v1/repositories/order.repository';
import { MerchantAccountRepository } from '@v1/repositories/merchant-account.repository';
import { ServiceRepository } from '@v1/repositories/service.repository';
import { ReminderRepository } from '@v1/repositories/reminder.repository';
import { injectable, inject } from 'inversify';
import 'reflect-metadata';
import CustomError from '@common/helpers/custom-error.helper';
import { UserRepository } from '@v1/repositories/user.repository';
import mongoose from 'mongoose';

@injectable()
class OrderService {
  private orderRepository;
  private serviceRepository;
  private merchantAccountRepository;
  private reminderRepository;
  private userRepository;

  constructor(
    @inject(TYPES.OrderRepository) orderRepository: OrderRepository,
    @inject(TYPES.ServiceRepository) serviceRepository: ServiceRepository,
    @inject(TYPES.MerchantAccountRepository) merchantAccountRepository: MerchantAccountRepository,
    @inject(TYPES.ReminderRepository) reminderRepository: ReminderRepository,
    @inject(TYPES.UserRepository) userRepository: UserRepository,
  ) {
    this.orderRepository = orderRepository;
    this.serviceRepository = serviceRepository;
    this.merchantAccountRepository = merchantAccountRepository;
    this.reminderRepository = reminderRepository;
    this.userRepository = userRepository;
  }

  async getOrderDetails(order: any) {
    const orderDetails: any = {};

    Object.keys(order).forEach((key: string) => {
      if (key === 'orderId') {
        orderDetails['_id'] = order.orderId;
      } else if (key === 'serviceId') {
        orderDetails['service'] = order[key];
      } else if (key === 'merchantId') {
        orderDetails['user'] = order[key];
      } else {
        orderDetails[key] = order[key];
      }
    });

    return orderDetails;
  }

  async getModifiedOrder(order: any, merchantService: any = null) {
    const service = merchantService || await this.serviceRepository.getById(order.service);

    return {
      _id: order._id,
      name: order.name,
      phoneNumber: order.phoneNumber,
      service: {
        name: service?.name || null,
        duration: service?.duration || null,
      },
      date: order.date,
      time: order.time,
      price: order.price,
      isCompleted: order.isCompleted,
      isFullDay: order.isFullDay,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    };
  }

  async createReminder(order: any, service: any) {
    const reminderDate = new Date();

    const reminderFrequency = parseInt(service.reminderFrequency);

    reminderDate.setDate(reminderDate.getDate() + reminderFrequency * 7); // to convert weeks to days

    await this.reminderRepository.create({
      user: order.user,
      date: reminderDate,
      frequency: reminderFrequency,
      phoneNumber: order.phoneNumber,
      name: order.name,
      order: order._id,
    });
  }

  async createOrder(resource: any) {
    const user = await this.userRepository.getById(resource.merchantId);
    resource.date = new Date(resource.date);

    resource.merchant = user.merchant;

    if (!mongoose.Types.ObjectId.isValid(resource.serviceId)) {
      throw new CustomError(404, 'Service not found');
    }

    const service: any = await this.serviceRepository.getById(resource.serviceId);

    if (!service) {
      throw new CustomError(404, 'Service not found');
    }

    const orderDetails = await this.getOrderDetails(resource);

    const order = await this.orderRepository.create(orderDetails);

    await this.createReminder(order, service);

    const modifiedOrder = await this.getModifiedOrder(order);

    return modifiedOrder;
  }

  async updateOrderById(resource: any) {
    const orderDetails = await this.getOrderDetails(resource);

    const order = await this.orderRepository.updateOrderById(orderDetails);

    if (!order) {
      throw new CustomError(404, 'Order not found');
    }

    return order;
  }

  async markOrderAsCompleted(id: string, merchantId: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new CustomError(404, 'Order not found');
    }

    const order = await this.orderRepository.getOrder({ _id: id, user: merchantId });
    if (!order) {
      throw new CustomError(404, 'Order not found');
    }

    if (order.isCompleted) {
      throw new CustomError(409, 'Order is already completed!');
    }

    const updatedOrder = await this.orderRepository.update({ _id: id, user: merchantId, isCompleted: true });

    return this.getModifiedOrder(updatedOrder, order.service);
  }

  async getOrders(userId: string) {
    return await this.orderRepository.getAllMerchantOrders(userId);
  }

  async getOrder(resource: any) {
    if (!mongoose.Types.ObjectId.isValid(resource.orderId)) {
      throw new CustomError(404, 'Order not found');
    }

    const orderDetails = await this.getOrderDetails(resource);
    const order = await this.orderRepository.getOrder(orderDetails);

    if (!order) {
      throw new CustomError(404, 'Order not found');
    }

    return order;
  }

  async cancelOrder(id: string, userId: string) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      throw new CustomError(404, 'Order not found');
    }

    if (!(await this.orderRepository.updateDeleteStatus(id, userId))) {
      throw new CustomError(404, 'Order not found');
    }
    await this.reminderRepository.deleteByOrderId(id);
  }
}

export { OrderService };
