import express from 'express';
import 'reflect-metadata';
import AuditLogRepository from '@v1/repositories/audit-log.repository';
import { CustomResponse } from '@common/interfaces/custom-response.interface';

export const logAudit = (activity: string) => {
  return async (req: express.Request, res: CustomResponse, next: express.NextFunction) => {
    try {
      await AuditLogRepository.create({ user: res.locals.jwt._id, activity });
      next();
    } catch (error) {
      res.handleErrorResponse(error);
    }
  };
};
