import { UserRole } from '@common/enums/index.enums';
import { CustomResponse } from '@common/interfaces/custom-response.interface';
import Auth from '@common/middleware/auth.middleware';
import UploadMiddleware from '@common/middleware/upload.middleware';
import { validate } from '@common/middleware/validate.middleware';
import TYPES from '@common/types/inversify.types';
import { ServiceSchema } from '@v1/dtos/service.dtos';
import { MerchantService } from '@v1/services/merchant-service.service';
import express from 'express';
import { inject } from 'inversify';
import { controller, httpDelete, httpGet, httpPatch, httpPost, request, response } from 'inversify-express-utils';

@controller('/services', Auth.validateAuth([UserRole.MERCHANT]))
class ServiceController {
  constructor(@inject(TYPES.MerchantService) private merchantService: MerchantService) {}

  @httpGet('')
  public async getMerchantServices(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const services = await this.merchantService.getMerchantServices(res.locals.jwt._id);
      res.handleSuccessResponse(200, `Services retrieved`, services);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpGet('/:service_id', validate(ServiceSchema.serviceId))
  public async getMerchantServiceById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.merchantService.getMerchantService({ merchantId: res.locals.jwt._id, serviceId: req.params.service_id });
      res.handleSuccessResponse(200, `Service retrieved`, service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPost('', UploadMiddleware.uploadReviewImage(), validate(ServiceSchema.createService))
  public async createService(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.merchantService.createService({ merchantId: res.locals.jwt._id, ...req.body }, req.file);
      res.handleSuccessResponse(201, 'Service created', service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('/review-image', UploadMiddleware.uploadReviewImage())
  public async updateReviewImage(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.merchantService.updateReviewImage({ merchantId: res.locals.jwt._id, ...req.body, file: req.file });
      res.handleSuccessResponse(200, 'Review image updated', service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpPatch('', validate(ServiceSchema.updateService))
  public async updateMerchantService(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.merchantService.updateMerchantService({ merchantId: res.locals.jwt._id, ...req.body });

      res.handleSuccessResponse(200, `Service updated`, service);
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }

  @httpDelete('/:service_id', validate(ServiceSchema.serviceId))
  public async deleteServiceById(@request() req: express.Request, @response() res: CustomResponse) {
    try {
      const service = await this.merchantService.deleteMerchantService(req.params.service_id, res.locals.jwt._id);
      res.handleSuccessResponse(200, 'Service deleted');
    } catch (error: any) {
      res.handleErrorResponse(error);
    }
  }
}
