import { Order } from '@common/schema/index.schema';
import { injectable } from 'inversify';
import 'reflect-metadata';
import BaseRepository from './base.repository';

@injectable()
class OrderRepository extends BaseRepository {
  constructor() {
    super(Order);
  }

  async findOrders(filter: any, limit: number, page: number) {
    return await Order.find(filter)
      .limit(limit)
      .skip(limit * (page - 1))
      .populate(
        'merchant',
        '-_id -__v -createdAt -updatedAt -email -user -subscriptionPlan -firstName -lastName -walletId -walletClientId -walletAccountNumber -address -phoneNumber -state -status -businessType -openingTime -closingTime -openingDays -instagramHandle -twitterHandle -facebookUsername',
      )
      .populate('service', '-_id -__v -reminderFrequency -createdAt -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user')
      .sort('-createdAt')
      .exec();
  }

  async getAllMerchantOrders(userId: string) {
    return await Order.find({ user: userId, isDeleted: false })
      .populate('service', '-_id -__v -createdAt -reminderFrequency -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user -merchant');
  }

  async getOrdersByMerchantId(merchantId: string, page: number, limit: number = 30) {
    const filter = {
      isDeleted: false,
      user: merchantId,
    };

    const orders = await this.findOrders(filter, limit, page);

    return await this.getPaginationDetails(filter, limit, page, orders);
  }

  async updateOrderById(resource: any) {
    const { _id, user, ...data } = resource;
    return await Order.findOneAndUpdate({ _id, user, isDeleted: false }, { $set: data }, { new: true })
      .populate('service', '-_id -__v -createdAt -reminderFrequency -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user')
      .exec();
  }

  async getOrder({ _id, user }: any) {
    return await Order.findOne({ _id, user, isDeleted: false })
      .populate('service', '-_id -__v -createdAt -reminderFrequency -updatedAt -description -price -isNegotiable -user -merchant')
      .select('-__v -user -merchant')
      .exec();
  }
}

export { OrderRepository };
