export type Email = {
  email: string;
};

export type UserLogin = {
  email: string;
  password: string;
};

export type ModifiedUser = {
  _id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  role: string | null | undefined;
  isVerified: boolean | null;
  createdAt: Date;
  updatedAt: Date;
  refreshKey?: Buffer;
};

export type ExistingUser = {
  _id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  role: string | null | undefined;
  isVerified: boolean | null;
  merchantId?: string;
  createdAt: Date;
  updatedAt: Date;
};

export type CreateUser = {
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  merchantId?: string;
  profilePicture?: object;
};

export type UpdateUserByMerchantId = {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  email?: string;
  password?: string;
  merchantId: string;
};

export type ProcessResponse = {
  isSuccessful: boolean;
  data?: any;
  error?: any;
};

export type requestMethod = 'GET' | 'POST';

export type carbonVirtualAccountOwner = {
  firstNameOnBvn: string;
  middlenameOnBvn: string;
  lastNameOnBvn: string;
  dateOfBirthOnBvn: string;
  phoneNumberOnBvn: string;
  emailOnBvn: string;
  genderOnBvn: 'Male' | 'Female' | 'male' | 'female';
  addressOnBvn: string;
  stateOnBvn: string;
  countryOnBvn: string;
  bvn: string;
};

export type carbonTransferPayload = {
  amount: number;
  walletId: string;
  beneficiaryAccountNumber: string;
  beneficiaryBankSortCode: string;
  reference: string;
  narration: string;
};

export type createVfdAccount = {
  bvn: string;
  dateOfBirthOnBVn: string;
};

export type Image = {
  id: string | null;
  url: string;
};
