import { z as schema } from 'zod';

const createSubscriptionPlan = schema.object({
  body: schema
    .object({
      name: schema.string(),
      amountPerMonth: schema.number(),
      description: schema.string(),
      features: schema.string().optional(),
    })
    .strict(),
});

const updateSubscriptionPlan = schema.object({
  body: schema
    .object({
      subscriptionPlans: schema
        .object({
          subscriptionPlanId: schema.string(),
          name: schema.string().optional(),
          amountPerMonth: schema.number().optional(),
          description: schema.string().optional(),
          features: schema.string().optional(),
        })
        .array()
        .nonempty(),
    })
    .strict(),
});

const subscriptionPlanId = schema.object({
  params: schema
    .object({
      subscription_plan_id: schema.string(),
    })
    .strict(),
});

export const SubscriptionPlanSchema = {
  createSubscriptionPlan,
  updateSubscriptionPlan,
  subscriptionPlanId,
};
