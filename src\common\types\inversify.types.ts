import { InvoiceRepository } from "@v1/repositories/invoice.repository";

const TYPES = {
  DatabaseConnectionType: Symbol.for('DatabaseConnection'),
  DatabaseSeeder: Symbol.for('DatabaseSeeder'),
  EmailNotifier: Symbol.for('EmailNotifier'),
  SeedDatabase: Symbol.for('SeedDatabase'),
  VirtualAccount: Symbol.for('VirtualAccount'),

  //repositories
  AdminAccountRepository: Symbol.for('AdminAccountRepository'),
  AuditLogRepository: Symbol.for('AuditLogRepository'),
  AuthTokenRepository: Symbol.for('AuthTokenRepository'),
  BusinessTypeRepository: Symbol.for('BusinessTypeRepository'),
  CommissionSettingRepository: Symbol.for('CommissionSettingRepository'),
  DeletedUserRepository: Symbol.for('DeletedUserRepository'),
  InvoiceRepository: Symbol.for('InvoiceRepository'),
  MerchantAccountRepository: Symbol.for('MerchantAccountRepository'),
  PotentialCustomerRepository: Symbol.for('PotentialCustomerRepository'),
  OtpRepository: Symbol.for('OtpRepository'),
  OrderRepository: Symbol.for('OrderRepository'),
  ServiceRepository: Symbol.for('ServiceRepository'),
  SubscriptionPlanRepository: Symbol.for('SubscriptionPlanRepository'),
  ReminderRepository: Symbol.for('ReminderRepository'),
  UserRepository: Symbol.for('UserRepository'),

  AuthMiddleware: Symbol.for('AuthMiddleware'),

  //services
  AdminBusinessTypeService: Symbol.for('AdminBusinessTypeService'),
  AdminDashboardService: Symbol.for('AdminDashboardService'),
  AdminMerchantService: Symbol.for('AdminMerchantService'),
  AdminInvoiceService: Symbol.for('AdminInvoiceService'),
  AdminPotentialCustomerService: Symbol.for('AdminPotentialCustomerService'),
  AdminService: Symbol.for('AdminService'),
  AdminSubscriptionPlanService: Symbol.for('AdminSubscriptionPlanService'),
  AuditLogService: Symbol.for('AuditLogService'),
  AuthService: Symbol.for('AuthService'),
  BusinessTypeService: Symbol.for('BusinessTypeService'),
  CommissionSettingService: Symbol.for('CommissionSettingService'),
  DashboardService: Symbol.for('DashboardService'),
  InvoiceService: Symbol.for('InvoiceService'),
  MerchantService: Symbol.for('MerchantService'),
  MerchantAccountService: Symbol.for('MerchantAccountService'),
  OrderService: Symbol.for('OrderService'),
  PictureUploadService: Symbol.for('PictureUploadService'),
  PotentialCustomerService: Symbol.for('PotentialCustomerService'),
  ReminderService: Symbol.for('ReminderService'),
  SubscriptionPlanService: Symbol.for('SubscriptionPlanService'),
  UserService: Symbol.for('UserService'),
  WalletService: Symbol.for('WalletService'),

  // mails
  AdminWelcomeMail: Symbol.for('AdminWelcomeMail'),
  EmailChangeRequestMail: Symbol.for('EmailChangeRequestMail'),
  EmailChangeSuccessMail: Symbol.for('EmailChangeSuccessMail'),
  MerchantSignInMail: Symbol.for('MerchantSignInMail'),
  MerchantWelcomeMail: Symbol.for('MerchantWelcomeMail'),
  SendAdminInviteMail: Symbol.for('SendAdminInviteMail'),
  UserAccountVerificationMail: Symbol.for('UserAccountVerificationMail'),
  UserPasswordChangeMail: Symbol.for('UserPasswordChangeMail'),
  UserPasswordResetMail: Symbol.for('UserPasswordResetMail'),
  UserSuccessfulPasswordChangeMail: Symbol.for('UserSuccessfulPasswordChangeMail'),
};

export default TYPES;
